import { NextRequest, NextResponse } from 'next/server';
import {
  runAutoSync,
  syncTemplatesOnDemand,
  getAutoSyncStatus
} from '@/lib/auto-sync';

/**
 * GET /api/admin/auto-sync - Get auto-sync status
 */
export async function GET() {
  try {
    const status = getAutoSyncStatus();
    
    return NextResponse.json({
      success: true,
      status,
      message: status.isRunning ? 'Auto-sync is running' : 'Auto-sync is idle'
    });
    
  } catch (error) {
    console.error('Error getting auto-sync status:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to get auto-sync status' 
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/auto-sync - Trigger auto-sync or control auto-sync
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;
    
    switch (action) {
      case 'sync-now':
        console.log('Manual auto-sync triggered');
        const result = await syncTemplatesOnDemand();
        
        return NextResponse.json({
          success: result.success,
          message: `Sync completed. ${result.syncedCount}/${result.totalTemplates} templates synced.`,
          result
        });
        
      case 'force-sync':
        console.log('Force sync triggered');
        const forceResult = await runAutoSync(true);
        
        return NextResponse.json({
          success: forceResult.errors.length === 0,
          message: `Force sync completed. ${forceResult.syncedCount}/${forceResult.totalTemplates} templates synced.`,
          result: forceResult
        });
        
      default:
        return NextResponse.json(
          { 
            success: false,
            error: 'Invalid action. Use "sync-now" or "force-sync"' 
          },
          { status: 400 }
        );
    }
    
  } catch (error) {
    console.error('Error in auto-sync API:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to execute auto-sync action' 
      },
      { status: 500 }
    );
  }
}
