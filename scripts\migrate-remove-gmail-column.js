const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Database path
const DB_PATH = path.join(__dirname, '..', 'ldis.db');

console.log('🗑️  LDIS Gmail Column Removal Migration');
console.log('=====================================');
console.log(`📍 Database path: ${DB_PATH}`);

function migrateDatabase() {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        console.error('❌ Error opening database:', err.message);
        reject(err);
        return;
      }

      console.log('✅ Connected to SQLite database');

      // Check if gmail column exists
      db.all(`PRAGMA table_info(users)`, (err, rows) => {
        if (err) {
          console.error('❌ Error checking users table:', err.message);
          reject(err);
          return;
        }

        const hasGmailColumn = rows.some(row => row.name === 'gmail');
        console.log(`📊 Gmail column exists: ${hasGmailColumn ? '✅ Yes' : '❌ No'}`);

        if (!hasGmailColumn) {
          console.log('ℹ️  Gmail column does not exist, no migration needed');
          db.close();
          resolve();
          return;
        }

        console.log('🔄 Starting gmail column removal migration...');

        // SQLite doesn't support DROP COLUMN directly, so we need to:
        // 1. Create a new table without the gmail column
        // 2. Copy data from old table to new table
        // 3. Drop old table
        // 4. Rename new table

        // Step 1: Create new users table without gmail column
        const createNewUsersTable = `
          CREATE TABLE users_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            recovery_key TEXT,
            role TEXT NOT NULL DEFAULT 'admin',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `;

        db.run(createNewUsersTable, (err) => {
          if (err) {
            console.error('❌ Error creating new users table:', err.message);
            reject(err);
            return;
          }

          console.log('✅ Created new users table without gmail column');

          // Step 2: Copy data from old table to new table (excluding gmail column)
          const copyData = `
            INSERT INTO users_new (id, username, password, recovery_key, role, created_at)
            SELECT id, username, password, recovery_key, role, created_at
            FROM users
          `;

          db.run(copyData, (err) => {
            if (err) {
              console.error('❌ Error copying data to new table:', err.message);
              reject(err);
              return;
            }

            console.log('✅ Copied data to new users table');

            // Step 3: Drop old table
            db.run('DROP TABLE users', (err) => {
              if (err) {
                console.error('❌ Error dropping old users table:', err.message);
                reject(err);
                return;
              }

              console.log('✅ Dropped old users table');

              // Step 4: Rename new table
              db.run('ALTER TABLE users_new RENAME TO users', (err) => {
                if (err) {
                  console.error('❌ Error renaming new table:', err.message);
                  reject(err);
                  return;
                }

                console.log('✅ Renamed new table to users');

                // Verify the migration
                db.all(`PRAGMA table_info(users)`, (err, rows) => {
                  if (err) {
                    console.error('❌ Error verifying migration:', err.message);
                    reject(err);
                    return;
                  }

                  const columns = rows.map(row => row.name);
                  console.log('📊 Final users table columns:', columns);

                  const stillHasGmail = rows.some(row => row.name === 'gmail');
                  if (stillHasGmail) {
                    console.error('❌ Migration failed: gmail column still exists');
                    reject(new Error('Migration failed: gmail column still exists'));
                    return;
                  }

                  console.log('🎉 Migration completed successfully!');
                  console.log('✅ Gmail column has been removed from users table');

                  db.close((err) => {
                    if (err) {
                      console.error('❌ Error closing database:', err.message);
                      reject(err);
                      return;
                    }
                    console.log('✅ Database connection closed');
                    resolve();
                  });
                });
              });
            });
          });
        });
      });
    });
  });
}

// Run the migration
migrateDatabase()
  .then(() => {
    console.log('\n🎉 Gmail column removal migration completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Migration failed:', error.message);
    process.exit(1);
  });
