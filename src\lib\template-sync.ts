import type { Template } from './database';
import {
  createTemplateInSupabase,
  getTemplateByIdFromSupabase,
  getTemplatesByUserIdFromSupabase,
  getAllTemplatesFromSupabase,
  updateTemplateInSupabase,
  deleteTemplateFromSupabase,
  getTemplatesWithUserInfoFromSupabase,
  syncTemplateToSupabase,
  syncTemplateToLocal,
  type TemplateWithUser
} from './supabase-templates';

/**
 * Create a new template with bidirectional sync
 */
export async function createTemplate(
  templateName: string,
  description: string | undefined,
  filename: string,
  placeholders: string | undefined,
  layoutSize: string | undefined,
  userId: number
): Promise<number> {
  try {
    // Import local database functions
    const { createTemplate: createTemplateLocal } = await import('./database');
    
    // Create template locally first
    const localTemplateId = await createTemplateLocal(templateName, description, filename, placeholders, layoutSize, userId);
    
    // Get the created template to sync to Supabase
    const { getTemplateById } = await import('./database');
    const createdTemplate = await getTemplateById(localTemplateId);
    
    if (createdTemplate) {
      // Sync to Supabase
      await syncTemplateToSupabase(createdTemplate);
    }
    
    return localTemplateId;
  } catch (error) {
    console.error('Error creating template with sync:', error);
    throw error;
  }
}

/**
 * Get a template by ID with fallback to both sources
 */
export async function getTemplateById(id: number): Promise<Template | undefined> {
  try {
    // Try local first
    const { getTemplateById: getTemplateByIdLocal } = await import('./database');
    let template = await getTemplateByIdLocal(id);
    
    if (!template) {
      // Try Supabase if not found locally
      template = await getTemplateByIdFromSupabase(id);
      
      if (template) {
        // Sync to local if found in Supabase
        await syncTemplateToLocal(template);
      }
    }
    
    return template;
  } catch (error) {
    console.error('Error getting template by ID:', error);
    throw error;
  }
}

/**
 * Get all templates for a specific user with sync from both sources
 */
export async function getTemplatesByUserId(userId: number): Promise<Template[]> {
  try {
    // Get templates from both sources
    const { getTemplatesByUserId: getTemplatesByUserIdLocal } = await import('./database');
    const localTemplates = await getTemplatesByUserIdLocal(userId);
    const supabaseTemplates = await getTemplatesByUserIdFromSupabase(userId);
    
    // Create a map to merge templates by ID
    const templateMap = new Map<number, Template>();
    
    // Add local templates first
    localTemplates.forEach(template => {
      templateMap.set(template.id, template);
    });
    
    // Add/update with Supabase templates
    supabaseTemplates.forEach(template => {
      const existingTemplate = templateMap.get(template.id);
      if (!existingTemplate || new Date(template.uploaded_at) > new Date(existingTemplate.uploaded_at)) {
        templateMap.set(template.id, template);
        // Sync to local if newer or missing
        syncTemplateToLocal(template).catch(console.error);
      }
    });
    
    // Sync local templates to Supabase if they don't exist there
    localTemplates.forEach(template => {
      const supabaseTemplate = supabaseTemplates.find(st => st.id === template.id);
      if (!supabaseTemplate) {
        syncTemplateToSupabase(template).catch(console.error);
      }
    });
    
    return Array.from(templateMap.values()).sort((a, b) => 
      new Date(b.uploaded_at).getTime() - new Date(a.uploaded_at).getTime()
    );
  } catch (error) {
    console.error('Error getting templates by user ID:', error);
    throw error;
  }
}

/**
 * Get all templates with sync from both sources
 */
export async function getAllTemplates(): Promise<Template[]> {
  try {
    // Get templates from both sources
    const { getAllTemplates: getAllTemplatesLocal } = await import('./database');
    const localTemplates = await getAllTemplatesLocal();
    const supabaseTemplates = await getAllTemplatesFromSupabase();
    
    // Create a map to merge templates by ID
    const templateMap = new Map<number, Template>();
    
    // Add local templates first
    localTemplates.forEach(template => {
      templateMap.set(template.id, template);
    });
    
    // Add/update with Supabase templates
    supabaseTemplates.forEach(template => {
      const existingTemplate = templateMap.get(template.id);
      if (!existingTemplate || new Date(template.uploaded_at) > new Date(existingTemplate.uploaded_at)) {
        templateMap.set(template.id, template);
        // Sync to local if newer or missing
        syncTemplateToLocal(template).catch(console.error);
      }
    });
    
    // Sync local templates to Supabase if they don't exist there
    localTemplates.forEach(template => {
      const supabaseTemplate = supabaseTemplates.find(st => st.id === template.id);
      if (!supabaseTemplate) {
        syncTemplateToSupabase(template).catch(console.error);
      }
    });
    
    return Array.from(templateMap.values()).sort((a, b) => 
      new Date(b.uploaded_at).getTime() - new Date(a.uploaded_at).getTime()
    );
  } catch (error) {
    console.error('Error getting all templates:', error);
    throw error;
  }
}

/**
 * Update template information with bidirectional sync
 */
export async function updateTemplate(
  id: number,
  templateName: string,
  description: string | undefined,
  placeholders: string | undefined,
  layoutSize: string | undefined
): Promise<void> {
  try {
    // Update locally
    const { updateTemplate: updateTemplateLocal } = await import('./database');
    await updateTemplateLocal(id, templateName, description, placeholders, layoutSize);
    
    // Update in Supabase
    await updateTemplateInSupabase(id, templateName, description, placeholders, layoutSize);
  } catch (error) {
    console.error('Error updating template:', error);
    throw error;
  }
}

/**
 * Delete a template with bidirectional sync
 */
export async function deleteTemplate(id: number): Promise<void> {
  try {
    // Delete locally
    const { deleteTemplate: deleteTemplateLocal } = await import('./database');
    await deleteTemplateLocal(id);
    
    // Delete from Supabase
    await deleteTemplateFromSupabase(id);
  } catch (error) {
    console.error('Error deleting template:', error);
    throw error;
  }
}

/**
 * Get templates with user information with sync from both sources
 */
export async function getTemplatesWithUserInfo(): Promise<TemplateWithUser[]> {
  try {
    // Get templates from both sources
    const { getTemplatesWithUserInfo: getTemplatesWithUserInfoLocal } = await import('./database');
    const localTemplates = await getTemplatesWithUserInfoLocal();
    const supabaseTemplates = await getTemplatesWithUserInfoFromSupabase();
    
    // Create a map to merge templates by ID
    const templateMap = new Map<number, TemplateWithUser>();
    
    // Add local templates first
    localTemplates.forEach(template => {
      templateMap.set(template.id, template);
    });
    
    // Add/update with Supabase templates
    supabaseTemplates.forEach(template => {
      const existingTemplate = templateMap.get(template.id);
      if (!existingTemplate || new Date(template.uploaded_at) > new Date(existingTemplate.uploaded_at)) {
        templateMap.set(template.id, template);
        // Sync to local if newer or missing
        syncTemplateToLocal(template).catch(console.error);
      }
    });
    
    // Sync local templates to Supabase if they don't exist there
    localTemplates.forEach(template => {
      const supabaseTemplate = supabaseTemplates.find(st => st.id === template.id);
      if (!supabaseTemplate) {
        syncTemplateToSupabase(template).catch(console.error);
      }
    });
    
    return Array.from(templateMap.values()).sort((a, b) => 
      new Date(b.uploaded_at).getTime() - new Date(a.uploaded_at).getTime()
    );
  } catch (error) {
    console.error('Error getting templates with user info:', error);
    throw error;
  }
}

/**
 * Sync all templates between local and Supabase
 */
export async function syncAllTemplates(): Promise<void> {
  try {
    console.log('Starting template synchronization...');
    
    // Get all templates to trigger sync
    await getAllTemplates();
    
    console.log('Template synchronization completed.');
  } catch (error) {
    console.error('Error during template synchronization:', error);
    throw error;
  }
}
