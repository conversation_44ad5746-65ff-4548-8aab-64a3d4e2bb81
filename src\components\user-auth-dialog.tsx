"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, Copy, Check } from "lucide-react";
import { toast } from "sonner";
import { useUserAuth } from "@/hooks/use-local-storage";
import { validateUsername, validatePassword } from "@/lib/validation";

interface UserAuthDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function UserAuthDialog({
  open,
  onClose,
  onSuccess,
}: UserAuthDialogProps) {
  const { authenticate } = useUserAuth();
  const [activeTab, setActiveTab] = useState("signin");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [generatedRecoveryKey, setGeneratedRecoveryKey] = useState("");
  const [copied, setCopied] = useState(false);

  // Form states for regular users only
  const [signInData, setSignInData] = useState({
    username: "",
    password: "",
  });
  const [signUpData, setSignUpData] = useState({
    username: "",
    password: "",
    recoveryKey: "",
  });
  const [forgotPasswordData, setForgotPasswordData] = useState({
    username: "",
    recoveryKey: "",
    newPassword: "",
  });

  const resetForms = () => {
    setSignInData({ username: "", password: "" });
    setSignUpData({
      username: "",
      password: "",
      recoveryKey: "",
    });
    setForgotPasswordData({
      username: "",
      recoveryKey: "",
      newPassword: "",
    });
    setError("");
    setGeneratedRecoveryKey("");
    setCopied(false);
  };

  const copyRecoveryKey = async () => {
    try {
      await navigator.clipboard.writeText(generatedRecoveryKey);
      setCopied(true);
      toast.success("Recovery key copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy recovery key");
    }
  };

  const handleClose = () => {
    resetForms();
    onClose();
  };

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Client-side validation
    const usernameValidation = validateUsername(signInData.username);
    if (!usernameValidation.isValid) {
      setError(usernameValidation.message!);
      setLoading(false);
      return;
    }

    // Validate that password is provided
    if (!signInData.password) {
      setError("Password is required");
      setLoading(false);
      return;
    }

    const passwordValidation = validatePassword(signInData.password);
    if (!passwordValidation.isValid) {
      setError(passwordValidation.message!);
      setLoading(false);
      return;
    }

    const signinPayload = {
      username: signInData.username,
      password: signInData.password,
    };

    try {
      const response = await fetch("/api/auth/signin", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(signinPayload),
      });

      const data = await response.json();

      if (response.ok) {
        // Only authenticate regular users through this dialog
        if (data.user.role === "regular") {
          authenticate({
            id: data.user.id,
            username: data.user.username,
            role: data.user.role,
          });
          toast.success("Signed in successfully!");
          onSuccess();
          resetForms();
        } else {
          setError(
            "Admin users should use the admin authentication in Settings."
          );
        }
      } else {
        toast.error(data.error || "Sign in failed");
        setError(data.error || "Sign in failed");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Client-side validation
    const usernameValidation = validateUsername(signUpData.username);
    if (!usernameValidation.isValid) {
      setError(usernameValidation.message!);
      setLoading(false);
      return;
    }

    const passwordValidation = validatePassword(signUpData.password);
    if (!passwordValidation.isValid) {
      setError(passwordValidation.message!);
      setLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          username: signUpData.username,
          password: signUpData.password,
          recoveryKey: signUpData.recoveryKey,
          role: "regular", // Always regular for this dialog
        }),
      });

      const data = await response.json();

      if (response.ok) {
        if (data.recoveryKey) {
          setGeneratedRecoveryKey(data.recoveryKey);
        }

        authenticate({
          id: data.user.id,
          username: data.user.username,
          role: data.user.role,
        });

        toast.success("Account created successfully!");
        // Don't call onSuccess or reset forms immediately if recovery key was generated - let user see it first
        if (!data.recoveryKey) {
          onSuccess();
          resetForms();
        }
      } else {
        toast.error(data.error || "Sign up failed");
        setError(data.error || "Sign up failed");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Validate all fields for password reset
    if (!forgotPasswordData.recoveryKey || !forgotPasswordData.newPassword) {
      setError("Please fill in all required fields");
      setLoading(false);
      return;
    }

    const passwordValidation = validatePassword(forgotPasswordData.newPassword);
    if (!passwordValidation.isValid) {
      setError(passwordValidation.message!);
      setLoading(false);
      return;
    }

    try {
      const response = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          username: forgotPasswordData.username,
          recoveryKey: forgotPasswordData.recoveryKey,
          newPassword: forgotPasswordData.newPassword,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Password reset successfully!");
        setActiveTab("signin");
        resetForms();
      } else {
        toast.error(data.error || "Password reset failed");
        setError(data.error || "Password reset failed");
      }
    } catch {
      toast.error("Network error. Please try again.");
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>User Authentication</DialogTitle>
          <DialogDescription>
            Sign in or create a regular user account to access LDIS features.
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {generatedRecoveryKey && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-3">
                <p className="font-medium">
                  Your recovery key has been generated:
                </p>
                <div className="relative">
                  <code className="block p-2 pr-12 bg-muted rounded text-sm break-all">
                    {generatedRecoveryKey}
                  </code>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={copyRecoveryKey}
                    className="absolute right-1 top-1 h-8 w-8 p-0"
                  >
                    {copied ? (
                      <Check className="h-4 w-4 text-green-600" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <p className="text-xs">
                  Please save this recovery key in a secure location. You'll
                  need it to recover your account if you forget your password.
                </p>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={copyRecoveryKey}
                  className="w-full"
                >
                  {copied ? (
                    <>
                      <Check className="mr-2 h-4 w-4 text-green-600" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <Copy className="mr-2 h-4 w-4" />
                      Copy Key
                    </>
                  )}
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="signin">Sign In</TabsTrigger>
            <TabsTrigger value="signup">Sign Up</TabsTrigger>
            <TabsTrigger value="forgot">Forgot Password</TabsTrigger>
          </TabsList>

          <TabsContent value="signin" className="space-y-4">
            <form onSubmit={handleSignIn} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="signin-username">Username</Label>
                <Input
                  id="signin-username"
                  type="text"
                  value={signInData.username}
                  onChange={(e) =>
                    setSignInData({ ...signInData, username: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="signin-password">Password</Label>
                <Input
                  id="signin-password"
                  type="password"
                  value={signInData.password}
                  onChange={(e) =>
                    setSignInData({ ...signInData, password: e.target.value })
                  }
                  required
                />
              </div>

              <Button type="submit" className="w-full" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Sign In
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="signup" className="space-y-4">
            <form
              onSubmit={
                generatedRecoveryKey ? (e) => e.preventDefault() : handleSignUp
              }
              className="space-y-4"
            >
              <div className="space-y-2">
                <Label htmlFor="signup-username">Username</Label>
                <Input
                  id="signup-username"
                  type="text"
                  value={signUpData.username}
                  onChange={(e) =>
                    setSignUpData({ ...signUpData, username: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="signup-password">Password</Label>
                <Input
                  id="signup-password"
                  type="password"
                  value={signUpData.password}
                  onChange={(e) =>
                    setSignUpData({ ...signUpData, password: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="signup-recovery-key">
                  Recovery Key (Optional)
                </Label>
                <Input
                  id="signup-recovery-key"
                  type="text"
                  value={signUpData.recoveryKey}
                  onChange={(e) =>
                    setSignUpData({
                      ...signUpData,
                      recoveryKey: e.target.value,
                    })
                  }
                  placeholder="Leave blank to auto-generate"
                />
                <p className="text-xs text-muted-foreground">
                  If left blank, a recovery key will be automatically generated
                  for password recovery.
                </p>
              </div>
              <Button
                type={generatedRecoveryKey ? "button" : "submit"}
                className="w-full"
                disabled={loading}
                onClick={
                  generatedRecoveryKey
                    ? () => {
                        setGeneratedRecoveryKey("");
                        resetForms();
                        onSuccess();
                      }
                    : undefined
                }
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {generatedRecoveryKey ? "Done" : "Create Account"}
              </Button>
            </form>
          </TabsContent>

          <TabsContent value="forgot" className="space-y-4">
            <form onSubmit={handleForgotPassword} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="forgot-username">Username</Label>
                <Input
                  id="forgot-username"
                  type="text"
                  value={forgotPasswordData.username}
                  onChange={(e) =>
                    setForgotPasswordData({
                      ...forgotPasswordData,
                      username: e.target.value,
                    })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="forgot-recovery-key">Recovery Key</Label>
                <Input
                  id="forgot-recovery-key"
                  type="text"
                  value={forgotPasswordData.recoveryKey}
                  onChange={(e) =>
                    setForgotPasswordData({
                      ...forgotPasswordData,
                      recoveryKey: e.target.value,
                    })
                  }
                  placeholder="Enter your recovery key"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="forgot-new-password">New Password</Label>
                <Input
                  id="forgot-new-password"
                  type="password"
                  value={forgotPasswordData.newPassword}
                  onChange={(e) =>
                    setForgotPasswordData({
                      ...forgotPasswordData,
                      newPassword: e.target.value,
                    })
                  }
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Reset Password
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
