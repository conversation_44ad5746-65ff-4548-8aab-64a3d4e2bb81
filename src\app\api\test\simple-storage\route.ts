import { NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

/**
 * GET /api/test/simple-storage - Simple storage test
 */
export async function GET() {
  try {
    console.log('Starting simple storage test...');
    
    // Test 1: List buckets
    console.log('Step 1: Listing buckets...');
    const { data: buckets, error: listError } = await supabaseAdmin.storage.listBuckets();
    
    if (listError) {
      console.error('List buckets error:', listError);
      return NextResponse.json({
        success: false,
        step: 'list-buckets',
        error: listError.message,
        details: listError
      }, { status: 500 });
    }
    
    console.log('Buckets:', buckets);
    
    // Test 2: Check if templates bucket exists
    const templatesBucket = buckets?.find(b => b.name === 'templates');
    if (!templatesBucket) {
      return NextResponse.json({
        success: false,
        step: 'check-bucket',
        error: 'Templates bucket not found',
        buckets: buckets?.map(b => b.name) || []
      }, { status: 404 });
    }
    
    console.log('Templates bucket found:', templatesBucket);
    
    // Test 3: Try to upload a simple test file
    console.log('Step 2: Uploading test file...');
    const testContent = `Test file created at ${new Date().toISOString()}`;
    const testBuffer = Buffer.from(testContent, 'utf-8');
    const testFileName = `test-${Date.now()}.txt`;
    
    const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
      .from('templates')
      .upload(testFileName, testBuffer, {
        contentType: 'text/plain',
        upsert: true
      });
    
    if (uploadError) {
      console.error('Upload error:', uploadError);
      return NextResponse.json({
        success: false,
        step: 'upload-file',
        error: uploadError.message,
        details: uploadError
      }, { status: 500 });
    }
    
    console.log('Upload successful:', uploadData);
    
    // Test 4: Try to download the file
    console.log('Step 3: Downloading test file...');
    const { data: downloadData, error: downloadError } = await supabaseAdmin.storage
      .from('templates')
      .download(testFileName);
    
    if (downloadError) {
      console.error('Download error:', downloadError);
      return NextResponse.json({
        success: false,
        step: 'download-file',
        error: downloadError.message,
        details: downloadError
      }, { status: 500 });
    }
    
    const downloadedContent = await downloadData.text();
    console.log('Downloaded content:', downloadedContent);
    
    // Test 5: Clean up - delete test file
    console.log('Step 4: Cleaning up...');
    const { error: deleteError } = await supabaseAdmin.storage
      .from('templates')
      .remove([testFileName]);
    
    if (deleteError) {
      console.error('Delete error:', deleteError);
      // Don't fail the test for cleanup errors
    }
    
    return NextResponse.json({
      success: true,
      message: 'Simple storage test completed successfully',
      results: {
        bucketsFound: buckets?.length || 0,
        templatesBucketExists: !!templatesBucket,
        uploadPath: uploadData?.path,
        downloadedContent: downloadedContent,
        contentMatches: downloadedContent === testContent
      }
    });
    
  } catch (error) {
    console.error('Simple storage test error:', error);
    return NextResponse.json({
      success: false,
      step: 'unexpected-error',
      error: error instanceof Error ? error.message : String(error),
      details: error
    }, { status: 500 });
  }
}

/**
 * POST /api/test/simple-storage - Test with custom file
 */
export async function POST(request: Request) {
  try {
    const { fileName, content } = await request.json();
    
    if (!fileName || !content) {
      return NextResponse.json({
        success: false,
        error: 'fileName and content are required'
      }, { status: 400 });
    }
    
    console.log(`Testing upload of custom file: ${fileName}`);
    
    const buffer = Buffer.from(content, 'utf-8');
    
    const { data, error } = await supabaseAdmin.storage
      .from('templates')
      .upload(`test/${fileName}`, buffer, {
        contentType: 'text/plain',
        upsert: true
      });
    
    if (error) {
      console.error('Custom upload error:', error);
      return NextResponse.json({
        success: false,
        error: error.message,
        details: error
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Custom file uploaded successfully',
      path: data.path
    });
    
  } catch (error) {
    console.error('Custom upload test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
