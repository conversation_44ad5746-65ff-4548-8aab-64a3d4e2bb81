#!/usr/bin/env node

/**
 * Initialize Supabase Storage for LDIS
 * This script sets up the templates storage bucket
 */

require('ts-node/register');

async function initializeSupabaseStorage() {
  try {
    console.log('🚀 Initializing Supabase Storage for LDIS...\n');

    // Import the storage module
    const { initializeTemplatesBucket } = require('../src/lib/supabase-storage.ts');

    // Initialize templates bucket
    console.log('📦 Setting up templates storage bucket...');
    await initializeTemplatesBucket();
    console.log('✅ Templates storage bucket initialized\n');

    console.log('🎉 Supabase Storage initialization completed successfully!');
    
  } catch (error) {
    console.error('❌ Error initializing Supabase Storage:', error);
    process.exit(1);
  }
}

initializeSupabaseStorage();
