'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error' | 'warning';
  message?: string;
  details?: any;
}

export default function StorageTestPage() {
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (name: string, status: TestResult['status'], message?: string, details?: any) => {
    setTests(prev => {
      const existing = prev.find(t => t.name === name);
      if (existing) {
        existing.status = status;
        existing.message = message;
        existing.details = details;
        return [...prev];
      } else {
        return [...prev, { name, status, message, details }];
      }
    });
  };

  const runTests = async () => {
    setIsRunning(true);
    setTests([]);

    try {
      // Test 1: Basic Supabase Connection
      updateTest('Connection Test', 'pending', 'Testing Supabase connection...');
      
      const connectionResponse = await fetch('/api/test/supabase-connection');
      const connectionResult = await connectionResponse.json();
      
      if (connectionResult.success) {
        updateTest('Connection Test', 'success', 'Supabase connection successful');
      } else {
        updateTest('Connection Test', 'error', connectionResult.error, connectionResult);
      }

      // Test 2: Storage Test
      updateTest('Storage Test', 'pending', 'Testing Supabase Storage...');
      
      const storageResponse = await fetch('/api/test/supabase-storage');
      const storageResult = await storageResponse.json();
      
      if (storageResult.success) {
        updateTest('Storage Test', 'success', 'Storage test completed successfully');
      } else {
        updateTest('Storage Test', 'error', storageResult.error, storageResult);
      }

      // Test 3: Initialize Templates Bucket
      updateTest('Bucket Initialization', 'pending', 'Initializing templates bucket...');
      
      const initResponse = await fetch('/api/admin/init-storage', { method: 'POST' });
      const initResult = await initResponse.json();
      
      if (initResponse.ok) {
        updateTest('Bucket Initialization', 'success', initResult.message);
      } else {
        updateTest('Bucket Initialization', 'error', initResult.error, initResult);
      }

      // Test 4: Storage Status
      updateTest('Storage Status', 'pending', 'Checking storage status...');
      
      const statusResponse = await fetch('/api/admin/storage?action=validate');
      const statusResult = await statusResponse.json();
      
      if (statusResponse.ok) {
        if (statusResult.valid) {
          updateTest('Storage Status', 'success', 'Storage integrity validated');
        } else {
          updateTest('Storage Status', 'warning', `Found ${statusResult.issues.length} issues`, statusResult);
        }
      } else {
        updateTest('Storage Status', 'error', 'Failed to check storage status', statusResult);
      }

    } catch (error) {
      updateTest('Test Suite', 'error', 'Unexpected error during testing', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    const variants = {
      pending: 'secondary',
      success: 'default',
      error: 'destructive',
      warning: 'outline'
    } as const;

    return (
      <Badge variant={variants[status]} className="ml-2">
        {status}
      </Badge>
    );
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Supabase Storage Test</h1>
        <p className="text-muted-foreground mt-2">
          Test and diagnose Supabase Storage functionality for LDIS
        </p>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Test Suite</CardTitle>
          <CardDescription>
            Run comprehensive tests to verify Supabase Storage is working correctly
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={runTests} 
            disabled={isRunning}
            className="w-full"
          >
            {isRunning ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Running Tests...
              </>
            ) : (
              'Run Storage Tests'
            )}
          </Button>
        </CardContent>
      </Card>

      {tests.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {tests.map((test, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 border rounded-lg">
                  <div className="mt-0.5">
                    {getStatusIcon(test.status)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center">
                      <h3 className="font-medium">{test.name}</h3>
                      {getStatusBadge(test.status)}
                    </div>
                    {test.message && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {test.message}
                      </p>
                    )}
                    {test.details && (
                      <details className="mt-2">
                        <summary className="text-sm cursor-pointer text-blue-600 hover:text-blue-800">
                          Show Details
                        </summary>
                        <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                          {JSON.stringify(test.details, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
