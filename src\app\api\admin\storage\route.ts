import { NextRequest, NextResponse } from 'next/server';
import {
  getAllTemplatesStorageStatus,
  validateStorageIntegrity,
  autoFixStorageIssues,
  cleanupOrphanedStorageFiles,
  forceLocalToStorage,
  forceStorageToLocal
} from '@/lib/storage-manager';

/**
 * GET /api/admin/storage - Get storage status and integrity information
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    
    switch (action) {
      case 'status':
        const statuses = await getAllTemplatesStorageStatus();
        return NextResponse.json({ statuses });
        
      case 'validate':
        const validation = await validateStorageIntegrity();
        return NextResponse.json(validation);
        
      default:
        // Default: return both status and validation
        const [allStatuses, integrity] = await Promise.all([
          getAllTemplatesStorageStatus(),
          validateStorageIntegrity()
        ]);
        
        return NextResponse.json({
          statuses: allStatuses,
          integrity
        });
    }
  } catch (error) {
    console.error('Error getting storage information:', error);
    return NextResponse.json(
      { error: 'Failed to get storage information' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/storage - Perform storage management actions
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, templateId, templateName } = body;
    
    switch (action) {
      case 'auto-fix':
        const fixResult = await autoFixStorageIssues();
        return NextResponse.json({
          message: `Auto-fix completed. Fixed ${fixResult.fixed} templates.`,
          ...fixResult
        });
        
      case 'cleanup-orphaned':
        const orphanedFiles = await cleanupOrphanedStorageFiles();
        return NextResponse.json({
          message: `Cleaned up ${orphanedFiles.length} orphaned files.`,
          orphanedFiles
        });
        
      case 'force-local-to-storage':
        if (!templateId || !templateName) {
          return NextResponse.json(
            { error: 'templateId and templateName are required' },
            { status: 400 }
          );
        }
        await forceLocalToStorage(templateId, templateName);
        return NextResponse.json({
          message: `Forced sync from local to storage for template ${templateId}`
        });
        
      case 'force-storage-to-local':
        if (!templateId) {
          return NextResponse.json(
            { error: 'templateId is required' },
            { status: 400 }
          );
        }
        await forceStorageToLocal(templateId);
        return NextResponse.json({
          message: `Forced sync from storage to local for template ${templateId}`
        });
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error performing storage action:', error);
    return NextResponse.json(
      { error: 'Failed to perform storage action' },
      { status: 500 }
    );
  }
}
