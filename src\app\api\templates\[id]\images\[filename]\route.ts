import { NextRequest, NextResponse } from 'next/server';
import { getTemplateById } from '@/lib/template-sync';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

/**
 * GET /api/templates/[id]/images/[filename] - Serve template image files
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string; filename: string }> }
) {
  try {
    const { id, filename } = await params;
    const templateId = parseInt(id);
    
    if (isNaN(templateId)) {
      return NextResponse.json(
        { error: 'Invalid template ID' },
        { status: 400 }
      );
    }

    // Get template from database
    const template = await getTemplateById(templateId);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Construct image file path
    const templatesDir = join(process.cwd(), 'public', 'templates');
    const templateDir = join(templatesDir, template.template_name);

    // Try multiple possible image locations
    let imagePath: string;
    let imageExists = false;

    // First try: images/ folder
    const imagesDir = join(templateDir, 'images');
    imagePath = join(imagesDir, filename);
    imageExists = existsSync(imagePath);

    // Second try: {template_name}_files/ folder (for templates exported from Word/other tools)
    if (!imageExists) {
      const templateFilesDir = join(templateDir, `${template.template_name}_files`);
      imagePath = join(templateFilesDir, filename);
      imageExists = existsSync(imagePath);
    }

    // Third try: directly in template directory
    if (!imageExists) {
      imagePath = join(templateDir, filename);
      imageExists = existsSync(imagePath);
    }

    // Check if image file exists
    if (!imageExists) {
      return NextResponse.json(
        { error: 'Image file not found' },
        { status: 404 }
      );
    }

    // Read image file
    const imageBuffer = await readFile(imagePath);

    // Determine content type based on file extension
    const ext = filename.toLowerCase().split('.').pop();
    let contentType = 'application/octet-stream';
    
    switch (ext) {
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg';
        break;
      case 'png':
        contentType = 'image/png';
        break;
      case 'gif':
        contentType = 'image/gif';
        break;
      case 'svg':
        contentType = 'image/svg+xml';
        break;
      case 'webp':
        contentType = 'image/webp';
        break;
    }

    // Return image with proper headers
    return new NextResponse(new Uint8Array(imageBuffer), {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    });

  } catch (error) {
    console.error('Error serving template image:', error);
    return NextResponse.json(
      { error: 'Failed to serve template image' },
      { status: 500 }
    );
  }
}
