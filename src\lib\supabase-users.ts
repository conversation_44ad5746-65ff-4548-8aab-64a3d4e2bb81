import { supabaseAdmin, handleSupabaseError } from './supabase';
import type { User } from './database';

// Supabase users interface
export interface SupabaseUser {
  id: number;
  username: string;
  password: string;
  recovery_key: string | null;
  role: 'admin' | 'regular';
  created_at: string;
}

/**
 * Create a new user in Supabase
 */
export async function createUserInSupabase(
  username: string,
  hashedPassword: string,
  recoveryKey?: string,
  role: 'admin' | 'regular' = 'admin'
): Promise<number> {
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .insert({
        username,
        password: hashedPassword,
        recovery_key: recoveryKey,
        role
      })
      .select('id')
      .single();

    if (error) {
      handleSupabaseError(error, 'create user in Supabase');
    }

    if (!data) {
      throw new Error('No data returned from Supabase');
    }

    return data.id;
  } catch (error) {
    handleSupabaseError(error, 'create user in Supabase');
    throw error;
  }
}

/**
 * Get a user by username from Supabase
 */
export async function getUserByUsernameFromSupabase(username: string): Promise<User | undefined> {
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('username', username)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return undefined;
      }
      handleSupabaseError(error, 'get user by username from Supabase');
    }

    if (!data) return undefined;

    return {
      id: data.id,
      username: data.username,
      password: data.password,
      recovery_key: data.recovery_key,
      role: data.role,
      created_at: data.created_at
    };
  } catch (error) {
    handleSupabaseError(error, 'get user by username from Supabase');
    throw error;
  }
}

/**
 * Get a user by ID from Supabase
 */
export async function getUserByIdFromSupabase(id: number): Promise<User | undefined> {
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return undefined;
      }
      handleSupabaseError(error, 'get user by ID from Supabase');
    }

    if (!data) return undefined;

    return {
      id: data.id,
      username: data.username,
      password: data.password,
      recovery_key: data.recovery_key,
      role: data.role,
      created_at: data.created_at
    };
  } catch (error) {
    handleSupabaseError(error, 'get user by ID from Supabase');
    throw error;
  }
}

/**
 * Get all users from Supabase
 */
export async function getAllUsersFromSupabase(): Promise<User[]> {
  try {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      handleSupabaseError(error, 'get all users from Supabase');
    }

    if (!data) return [];

    return data.map(item => ({
      id: item.id,
      username: item.username,
      password: item.password,
      recovery_key: item.recovery_key,
      role: item.role,
      created_at: item.created_at
    }));
  } catch (error) {
    handleSupabaseError(error, 'get all users from Supabase');
    throw error;
  }
}

/**
 * Update user password in Supabase
 */
export async function updateUserPasswordInSupabase(id: number, hashedPassword: string): Promise<void> {
  try {
    const { error } = await supabaseAdmin
      .from('users')
      .update({ password: hashedPassword })
      .eq('id', id);

    if (error) {
      handleSupabaseError(error, 'update user password in Supabase');
    }
  } catch (error) {
    handleSupabaseError(error, 'update user password in Supabase');
    throw error;
  }
}

/**
 * Update user recovery key in Supabase
 */
export async function updateUserRecoveryKeyInSupabase(id: number, recoveryKey: string): Promise<void> {
  try {
    const { error } = await supabaseAdmin
      .from('users')
      .update({ recovery_key: recoveryKey })
      .eq('id', id);

    if (error) {
      handleSupabaseError(error, 'update user recovery key in Supabase');
    }
  } catch (error) {
    handleSupabaseError(error, 'update user recovery key in Supabase');
    throw error;
  }
}

/**
 * Delete a user from Supabase
 */
export async function deleteUserFromSupabase(id: number): Promise<void> {
  try {
    const { error } = await supabaseAdmin
      .from('users')
      .delete()
      .eq('id', id);

    if (error) {
      handleSupabaseError(error, 'delete user from Supabase');
    }
  } catch (error) {
    handleSupabaseError(error, 'delete user from Supabase');
    throw error;
  }
}

/**
 * Sync user from local SQLite to Supabase
 */
export async function syncUserToSupabase(user: User): Promise<void> {
  try {
    // Check if user already exists in Supabase
    const existingUser = await getUserByIdFromSupabase(user.id);
    
    if (existingUser) {
      // Update existing user
      const { error } = await supabaseAdmin
        .from('users')
        .update({
          username: user.username,
          password: user.password,
          recovery_key: user.recovery_key,
          role: user.role,
          created_at: user.created_at
        })
        .eq('id', user.id);

      if (error) {
        handleSupabaseError(error, 'sync user to Supabase (update)');
      }
    } else {
      // Insert new user with original ID
      const { error } = await supabaseAdmin
        .from('users')
        .insert({
          id: user.id,
          username: user.username,
          password: user.password,
          recovery_key: user.recovery_key,
          role: user.role,
          created_at: user.created_at
        });

      if (error) {
        handleSupabaseError(error, 'sync user to Supabase (insert)');
      }
    }
  } catch (error) {
    handleSupabaseError(error, 'sync user to Supabase');
    throw error;
  }
}

/**
 * Sync user from Supabase to local SQLite
 */
export async function syncUserToLocal(user: User): Promise<void> {
  try {
    // Import local database functions
    const { getUserById, createUser, runQuery } = await import('./database');
    
    // Check if user already exists locally
    const existingUser = await getUserById(user.id);
    
    if (existingUser) {
      // Update existing user
      await runQuery(
        'UPDATE users SET username = ?, password = ?, recovery_key = ?, role = ?, created_at = ? WHERE id = ?',
        [user.username, user.password, user.recovery_key, user.role, user.created_at, user.id]
      );
    } else {
      // Insert new user with original ID
      await runQuery(
        'INSERT INTO users (id, username, password, recovery_key, role, created_at) VALUES (?, ?, ?, ?, ?, ?)',
        [user.id, user.username, user.password, user.recovery_key, user.role, user.created_at]
      );
    }
  } catch (error) {
    console.error('Error syncing user to local database:', error);
    throw error;
  }
}
