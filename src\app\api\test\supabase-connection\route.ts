import { NextResponse } from 'next/server';
import { supabaseAdmin, supabase } from '@/lib/supabase';

/**
 * GET /api/test/supabase-connection - Test basic Supabase connection
 */
export async function GET() {
  try {
    console.log('Testing Supabase connection...');
    
    // Test environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    console.log('Environment check:');
    console.log('- SUPABASE_URL:', supabaseUrl ? 'Set' : 'Missing');
    console.log('- ANON_KEY:', supabaseAnonKey ? 'Set' : 'Missing');
    console.log('- SERVICE_KEY:', supabaseServiceKey ? 'Set' : 'Missing');
    
    if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceKey) {
      return NextResponse.json({
        success: false,
        error: 'Missing Supabase environment variables',
        env: {
          url: !!supabaseUrl,
          anonKey: !!supabaseAnonKey,
          serviceKey: !!supabaseServiceKey
        }
      }, { status: 500 });
    }
    
    // Test 1: Basic database connection with anon client
    console.log('Test 1: Testing anon client database connection...');
    try {
      const { data: anonData, error: anonError } = await supabase
        .from('saved_files')
        .select('count')
        .limit(1);
      
      console.log('Anon client test result:', { data: anonData, error: anonError });
    } catch (anonTestError) {
      console.error('Anon client test error:', anonTestError);
    }
    
    // Test 2: Basic database connection with admin client
    console.log('Test 2: Testing admin client database connection...');
    try {
      const { data: adminData, error: adminError } = await supabaseAdmin
        .from('saved_files')
        .select('count')
        .limit(1);
      
      console.log('Admin client test result:', { data: adminData, error: adminError });
    } catch (adminTestError) {
      console.error('Admin client test error:', adminTestError);
    }
    
    // Test 3: Storage connection test
    console.log('Test 3: Testing storage connection...');
    let storageTest = { success: false, error: null, data: null };
    
    try {
      const { data: buckets, error: storageError } = await supabaseAdmin.storage.listBuckets();
      
      if (storageError) {
        storageTest = { success: false, error: storageError, data: null };
        console.error('Storage test error:', storageError);
      } else {
        storageTest = { success: true, error: null, data: buckets };
        console.log('Storage test success:', buckets);
      }
    } catch (storageTestError) {
      storageTest = { success: false, error: storageTestError, data: null };
      console.error('Storage test exception:', storageTestError);
    }
    
    // Test 4: Network connectivity test
    console.log('Test 4: Testing network connectivity...');
    let networkTest = { success: false, error: null };
    
    try {
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'GET',
        headers: {
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`
        }
      });
      
      if (response.ok) {
        networkTest = { success: true, error: null };
        console.log('Network test success');
      } else {
        networkTest = { success: false, error: `HTTP ${response.status}: ${response.statusText}` };
        console.error('Network test failed:', response.status, response.statusText);
      }
    } catch (networkError) {
      networkTest = { success: false, error: networkError };
      console.error('Network test exception:', networkError);
    }
    
    return NextResponse.json({
      success: storageTest.success,
      message: 'Supabase connection test completed',
      tests: {
        environment: {
          success: true,
          url: !!supabaseUrl,
          anonKey: !!supabaseAnonKey,
          serviceKey: !!supabaseServiceKey
        },
        network: networkTest,
        storage: storageTest
      },
      supabaseUrl: supabaseUrl
    });
    
  } catch (error) {
    console.error('Unexpected error during Supabase connection test:', error);
    return NextResponse.json({
      success: false,
      error: 'Unexpected error during connection test',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
