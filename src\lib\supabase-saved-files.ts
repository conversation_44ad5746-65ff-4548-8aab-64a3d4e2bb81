import { supabaseAdmin, bufferToBase<PERSON>, base64To<PERSON>uffer, handleSupabaseError } from './supabase';
import type { SavedFileMetadata } from './database';

// Supabase saved files interface
export interface SupabaseSavedFile {
  id: number;
  document_data: string; // Base64 encoded
  document_metadata: string | null;
  user_id: number;
  created_at: string;
}

// Local saved files interface (for compatibility)
export interface SavedFile {
  id: number;
  document_data: Buffer;
  document_metadata?: string;
  user_id: number;
  created_at: string;
}

/**
 * Create a new saved file in Supabase
 */
export async function createSavedFile(
  documentData: Buffer,
  metadata: SavedFileMetadata,
  userId: number
): Promise<number> {
  try {
    const base64Data = bufferToBase64(documentData);
    
    const { data, error } = await supabaseAdmin
      .from('saved_files')
      .insert({
        document_data: base64Data,
        document_metadata: JSON.stringify(metadata),
        user_id: userId
      })
      .select('id')
      .single();

    if (error) {
      handleSupabaseError(error, 'create saved file');
    }

    return data.id;
  } catch (error) {
    handleSupabaseError(error, 'create saved file');
    throw error;
  }
}

/**
 * Get a saved file by ID from Supabase
 */
export async function getSavedFileById(id: number): Promise<SavedFile | undefined> {
  try {
    const { data, error } = await supabaseAdmin
      .from('saved_files')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return undefined;
      }
      handleSupabaseError(error, 'get saved file by ID');
    }

    if (!data) return undefined;

    // Convert base64 back to Buffer for compatibility
    return {
      id: data.id,
      document_data: base64ToBuffer(data.document_data),
      document_metadata: data.document_metadata,
      user_id: data.user_id,
      created_at: data.created_at
    };
  } catch (error) {
    handleSupabaseError(error, 'get saved file by ID');
    throw error;
  }
}

/**
 * Get all saved files for a user from Supabase
 */
export async function getSavedFilesByUserId(userId: number): Promise<SavedFile[]> {
  try {
    const { data, error } = await supabaseAdmin
      .from('saved_files')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      handleSupabaseError(error, 'get saved files by user ID');
    }

    if (!data) return [];

    // Convert base64 back to Buffer for compatibility
    return data.map(item => ({
      id: item.id,
      document_data: base64ToBuffer(item.document_data),
      document_metadata: item.document_metadata,
      user_id: item.user_id,
      created_at: item.created_at
    }));
  } catch (error) {
    handleSupabaseError(error, 'get saved files by user ID');
    throw error;
  }
}

/**
 * Delete a saved file from Supabase
 */
export async function deleteSavedFile(id: number): Promise<void> {
  try {
    const { error } = await supabaseAdmin
      .from('saved_files')
      .delete()
      .eq('id', id);

    if (error) {
      handleSupabaseError(error, 'delete saved file');
    }
  } catch (error) {
    handleSupabaseError(error, 'delete saved file');
    throw error;
  }
}

/**
 * Delete all saved files for a user from Supabase
 */
export async function deleteSavedFilesByUserId(userId: number): Promise<void> {
  try {
    const { error } = await supabaseAdmin
      .from('saved_files')
      .delete()
      .eq('user_id', userId);

    if (error) {
      handleSupabaseError(error, 'delete saved files by user ID');
    }
  } catch (error) {
    handleSupabaseError(error, 'delete saved files by user ID');
    throw error;
  }
}

/**
 * Migrate existing saved files from SQLite to Supabase
 */
export async function migrateSavedFilesToSupabase(): Promise<void> {
  try {
    // Import SQLite functions
    const { getAllRows, getDatabase } = await import('./database');
    
    console.log('Starting saved files migration to Supabase...');
    
    // Get all saved files from SQLite
    const sqliteFiles = await getAllRows<SavedFile>('SELECT * FROM saved_files ORDER BY created_at ASC');
    
    if (sqliteFiles.length === 0) {
      console.log('No saved files found in SQLite database.');
      return;
    }

    console.log(`Found ${sqliteFiles.length} saved files to migrate.`);

    // Migrate each file
    let migratedCount = 0;
    for (const file of sqliteFiles) {
      try {
        // Check if file already exists in Supabase
        const existingFile = await getSavedFileById(file.id);
        if (existingFile) {
          console.log(`Saved file ${file.id} already exists in Supabase, skipping.`);
          continue;
        }

        // Parse metadata
        let metadata: SavedFileMetadata;
        try {
          metadata = file.document_metadata ? JSON.parse(file.document_metadata) : {};
        } catch (e) {
          console.warn(`Invalid metadata for saved file ${file.id}, using empty metadata.`);
          metadata = {} as SavedFileMetadata;
        }

        // Insert into Supabase with original ID
        const { error } = await supabaseAdmin
          .from('saved_files')
          .insert({
            id: file.id,
            document_data: bufferToBase64(file.document_data),
            document_metadata: file.document_metadata,
            user_id: file.user_id,
            created_at: file.created_at
          });

        if (error) {
          console.error(`Failed to migrate saved file ${file.id}:`, error);
          continue;
        }

        migratedCount++;
        console.log(`Migrated saved file ${file.id} (${migratedCount}/${sqliteFiles.length})`);
      } catch (error) {
        console.error(`Error migrating saved file ${file.id}:`, error);
      }
    }

    console.log(`Migration completed. ${migratedCount} saved files migrated successfully.`);
  } catch (error) {
    console.error('Error during saved files migration:', error);
    throw error;
  }
}
