'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, RefreshCw, CheckCircle, AlertTriangle, Clock } from 'lucide-react';

interface SyncStatus {
  isRunning: boolean;
  lastSyncTime: number;
  timeSinceLastSync: number;
  nextSyncIn: number;
}

interface SyncResult {
  success: boolean;
  syncedCount: number;
  totalTemplates: number;
  errors: string[];
}

export default function SyncManagementPage() {
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastResult, setLastResult] = useState<SyncResult | null>(null);
  const [message, setMessage] = useState<string>('');

  const fetchSyncStatus = async () => {
    try {
      const response = await fetch('/api/admin/auto-sync');
      const data = await response.json();
      
      if (data.success) {
        setSyncStatus(data.status);
      }
    } catch (error) {
      console.error('Error fetching sync status:', error);
    }
  };

  const triggerSync = async () => {
    setIsLoading(true);
    setMessage('');
    
    try {
      const response = await fetch('/api/admin/sync-now', { method: 'POST' });
      const data = await response.json();
      
      setLastResult(data.details || data);
      setMessage(data.message);
      
      // Refresh status after sync
      setTimeout(fetchSyncStatus, 1000);
      
    } catch (error) {
      setMessage('Failed to trigger sync: ' + error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (timestamp: number) => {
    if (!timestamp) return 'Never';
    return new Date(timestamp).toLocaleString();
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  useEffect(() => {
    fetchSyncStatus();
    const interval = setInterval(fetchSyncStatus, 10000); // Update every 10 seconds
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Template Auto-Sync Management</h1>
        <p className="text-muted-foreground mt-2">
          Manage automatic synchronization of templates to Supabase Storage for online access
        </p>
      </div>

      {/* Sync Status Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            Auto-Sync Status
          </CardTitle>
          <CardDescription>
            Current status of the automatic template synchronization service
          </CardDescription>
        </CardHeader>
        <CardContent>
          {syncStatus ? (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <span className="font-medium">Status:</span>
                <Badge variant={syncStatus.isRunning ? "default" : "secondary"}>
                  {syncStatus.isRunning ? "Running" : "Idle"}
                </Badge>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Last Sync</p>
                    <p className="text-sm text-muted-foreground">
                      {formatTime(syncStatus.lastSyncTime)}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Time Since Last Sync</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDuration(syncStatus.timeSinceLastSync)}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Next Sync In</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDuration(syncStatus.nextSyncIn)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading sync status...</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Manual Sync Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Manual Sync</CardTitle>
          <CardDescription>
            Trigger an immediate synchronization of all templates to Supabase Storage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={triggerSync} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Syncing Templates...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Sync All Templates Now
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Messages */}
      {message && (
        <Alert className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{message}</AlertDescription>
        </Alert>
      )}

      {/* Last Sync Result */}
      {lastResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {lastResult.success ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
              )}
              Last Sync Result
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">Templates Synced</p>
                  <p className="text-2xl font-bold text-green-600">
                    {lastResult.syncedCount}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Total Templates</p>
                  <p className="text-2xl font-bold">
                    {lastResult.totalTemplates}
                  </p>
                </div>
              </div>
              
              {lastResult.errors && lastResult.errors.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-red-600 mb-2">Errors:</p>
                  <ul className="text-sm text-red-600 space-y-1">
                    {lastResult.errors.map((error, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span>•</span>
                        <span>{error}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
