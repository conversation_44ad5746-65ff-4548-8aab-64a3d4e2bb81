import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Import startup to ensure auto-sync is initialized
import './lib/startup';

export function middleware(request: NextRequest) {
  // Let the request continue normally
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
