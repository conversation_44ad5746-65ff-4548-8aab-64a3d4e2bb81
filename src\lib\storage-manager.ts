import { join } from 'path';
import { existsSync } from 'fs';
import { readdir, stat } from 'fs/promises';
import {
  initializeTemplatesBucket,
  syncTemplateFilesToStorage,
  syncTemplateFilesFromStorage,
  templateFilesExistInStorage,
  listFilesInStorage,
  deleteFileFromStorage
} from './supabase-storage';

/**
 * Storage Manager for LDIS Template Files
 * Handles synchronization between local filesystem and Supabase Storage
 */

export interface StorageStatus {
  templateId: number;
  templateName: string;
  localFiles: string[];
  storageFiles: string[];
  syncStatus: 'synced' | 'local-only' | 'storage-only' | 'out-of-sync';
  lastLocalModified?: Date;
  lastStorageModified?: Date;
}

/**
 * Get storage status for a specific template
 */
export async function getTemplateStorageStatus(templateId: number, templateName: string): Promise<StorageStatus> {
  const templatesDir = join(process.cwd(), 'public', 'templates');
  const templateDir = join(templatesDir, templateName);
  
  // Get local files
  let localFiles: string[] = [];
  if (existsSync(templateDir)) {
    try {
      const files = await readdir(templateDir);
      localFiles = files.filter(file => !file.startsWith('.'));
    } catch (error) {
      console.error(`Error reading local template directory for ${templateId}:`, error);
    }
  }
  
  // Get storage files
  let storageFiles: string[] = [];
  try {
    const files = await listFilesInStorage(templateId.toString());
    storageFiles = files
      .filter(file => file.name && !file.name.endsWith('/'))
      .map(file => file.name!);
  } catch (error) {
    console.error(`Error listing storage files for template ${templateId}:`, error);
  }
  
  // Determine sync status
  let syncStatus: StorageStatus['syncStatus'] = 'synced';
  
  if (localFiles.length === 0 && storageFiles.length === 0) {
    syncStatus = 'synced'; // Both empty
  } else if (localFiles.length > 0 && storageFiles.length === 0) {
    syncStatus = 'local-only';
  } else if (localFiles.length === 0 && storageFiles.length > 0) {
    syncStatus = 'storage-only';
  } else {
    // Both have files, check if they match
    const localSet = new Set(localFiles);
    const storageSet = new Set(storageFiles);
    
    const hasAllLocal = [...localSet].every(file => storageSet.has(file));
    const hasAllStorage = [...storageSet].every(file => localSet.has(file));
    
    if (!hasAllLocal || !hasAllStorage) {
      syncStatus = 'out-of-sync';
    }
  }
  
  return {
    templateId,
    templateName,
    localFiles,
    storageFiles,
    syncStatus
  };
}

/**
 * Get storage status for all templates
 */
export async function getAllTemplatesStorageStatus(): Promise<StorageStatus[]> {
  try {
    // Get all templates from database
    const { getAllTemplates } = await import('./database');
    const templates = await getAllTemplates();
    
    const statusPromises = templates.map(template =>
      getTemplateStorageStatus(template.id, template.template_name)
    );
    
    return await Promise.all(statusPromises);
  } catch (error) {
    console.error('Error getting all templates storage status:', error);
    throw error;
  }
}

/**
 * Force sync a template from local to storage
 */
export async function forceLocalToStorage(templateId: number, templateName: string): Promise<void> {
  try {
    await initializeTemplatesBucket();
    await syncTemplateFilesToStorage(templateId, templateName);
    console.log(`Forced sync from local to storage for template ${templateId}`);
  } catch (error) {
    console.error(`Error forcing local to storage sync for template ${templateId}:`, error);
    throw error;
  }
}

/**
 * Force sync a template from storage to local
 */
export async function forceStorageToLocal(templateId: number): Promise<void> {
  try {
    await syncTemplateFilesFromStorage(templateId);
    console.log(`Forced sync from storage to local for template ${templateId}`);
  } catch (error) {
    console.error(`Error forcing storage to local sync for template ${templateId}:`, error);
    throw error;
  }
}

/**
 * Clean up orphaned files in storage (files that don't have corresponding templates)
 */
export async function cleanupOrphanedStorageFiles(): Promise<string[]> {
  try {
    // Get all templates from database
    const { getAllTemplates } = await import('./database');
    const templates = await getAllTemplates();
    const validTemplateIds = new Set(templates.map(t => t.id.toString()));
    
    // Get all directories in storage
    const storageDirectories = await listFilesInStorage('');
    const orphanedFiles: string[] = [];
    
    for (const dir of storageDirectories) {
      if (dir.name && dir.name.endsWith('/')) {
        const templateId = dir.name.replace('/', '');
        
        if (!validTemplateIds.has(templateId)) {
          // This is an orphaned directory
          try {
            const files = await listFilesInStorage(templateId);
            for (const file of files) {
              if (file.name && !file.name.endsWith('/')) {
                const filePath = `${templateId}/${file.name}`;
                await deleteFileFromStorage(filePath);
                orphanedFiles.push(filePath);
              }
            }
          } catch (error) {
            console.error(`Error cleaning up orphaned directory ${templateId}:`, error);
          }
        }
      }
    }
    
    console.log(`Cleaned up ${orphanedFiles.length} orphaned files from storage`);
    return orphanedFiles;
  } catch (error) {
    console.error('Error cleaning up orphaned storage files:', error);
    throw error;
  }
}

/**
 * Validate storage integrity
 */
export async function validateStorageIntegrity(): Promise<{
  valid: boolean;
  issues: string[];
  summary: {
    totalTemplates: number;
    syncedTemplates: number;
    localOnlyTemplates: number;
    storageOnlyTemplates: number;
    outOfSyncTemplates: number;
  };
}> {
  try {
    const statuses = await getAllTemplatesStorageStatus();
    const issues: string[] = [];
    
    let syncedCount = 0;
    let localOnlyCount = 0;
    let storageOnlyCount = 0;
    let outOfSyncCount = 0;
    
    for (const status of statuses) {
      switch (status.syncStatus) {
        case 'synced':
          syncedCount++;
          break;
        case 'local-only':
          localOnlyCount++;
          issues.push(`Template ${status.templateId} (${status.templateName}) has files only locally`);
          break;
        case 'storage-only':
          storageOnlyCount++;
          issues.push(`Template ${status.templateId} (${status.templateName}) has files only in storage`);
          break;
        case 'out-of-sync':
          outOfSyncCount++;
          issues.push(`Template ${status.templateId} (${status.templateName}) has mismatched files between local and storage`);
          break;
      }
    }
    
    return {
      valid: issues.length === 0,
      issues,
      summary: {
        totalTemplates: statuses.length,
        syncedTemplates: syncedCount,
        localOnlyTemplates: localOnlyCount,
        storageOnlyTemplates: storageOnlyCount,
        outOfSyncTemplates: outOfSyncCount
      }
    };
  } catch (error) {
    console.error('Error validating storage integrity:', error);
    throw error;
  }
}

/**
 * Auto-fix storage issues by syncing files
 */
export async function autoFixStorageIssues(): Promise<{
  fixed: number;
  errors: string[];
}> {
  try {
    const statuses = await getAllTemplatesStorageStatus();
    let fixedCount = 0;
    const errors: string[] = [];
    
    for (const status of statuses) {
      try {
        switch (status.syncStatus) {
          case 'local-only':
            await forceLocalToStorage(status.templateId, status.templateName);
            fixedCount++;
            break;
          case 'storage-only':
            await forceStorageToLocal(status.templateId);
            fixedCount++;
            break;
          case 'out-of-sync':
            // For out-of-sync, prefer local files (sync local to storage)
            await forceLocalToStorage(status.templateId, status.templateName);
            fixedCount++;
            break;
        }
      } catch (error) {
        errors.push(`Failed to fix template ${status.templateId}: ${error}`);
      }
    }
    
    return { fixed: fixedCount, errors };
  } catch (error) {
    console.error('Error auto-fixing storage issues:', error);
    throw error;
  }
}
