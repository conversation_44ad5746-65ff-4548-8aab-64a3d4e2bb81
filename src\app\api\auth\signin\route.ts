import { NextRequest, NextResponse } from 'next/server';
import { getUserByUsername } from '@/lib/user-sync';
import bcrypt from 'bcryptjs';

/**
 * POST /api/auth/signin - Authenticate user
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password, recoveryKey } = body;
    
    // Validate required fields
    if (!username) {
      return NextResponse.json(
        { error: 'Username is required' },
        { status: 400 }
      );
    }
    
    // Get user from database
    const user = await getUserByUsername(username);
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid username or password' },
        { status: 401 }
      );
    }
    
    // Verify authentication based on user role
    let authenticationValid = false;

    if (user.role === 'regular') {
      // Regular users can only authenticate with password
      if (!password) {
        return NextResponse.json(
          { error: 'Password is required for regular users' },
          { status: 400 }
        );
      }
      authenticationValid = await bcrypt.compare(password, user.password);
    } else if (user.role === 'admin') {
      // Admin users can authenticate with either password or recovery key
      if (!password && !recoveryKey) {
        return NextResponse.json(
          { error: 'Password or recovery key is required for admin users' },
          { status: 400 }
        );
      }

      if (password) {
        authenticationValid = await bcrypt.compare(password, user.password);
      } else if (recoveryKey && user.recovery_key) {
        authenticationValid = recoveryKey === user.recovery_key;
      }
    }

    if (!authenticationValid) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Authentication successful
    return NextResponse.json(
      { 
        message: 'Sign in successful',
        user: {
          id: user.id,
          username: user.username,
          role: user.role,
          created_at: user.created_at
        }
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error during sign in:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
