import { NextResponse } from 'next/server';
import { syncTemplatesOnDemand } from '@/lib/auto-sync';

/**
 * POST /api/admin/sync-now - Immediately sync all templates to storage
 */
export async function POST() {
  try {
    console.log('🔄 Starting immediate template sync...');
    
    const result = await syncTemplatesOnDemand();
    
    if (result.success) {
      console.log(`✅ Sync completed successfully: ${result.syncedCount}/${result.totalTemplates} templates synced`);
      
      return NextResponse.json({
        success: true,
        message: `Successfully synced ${result.syncedCount} out of ${result.totalTemplates} templates to Supabase Storage`,
        details: {
          syncedCount: result.syncedCount,
          totalTemplates: result.totalTemplates,
          errors: result.errors
        }
      });
    } else {
      console.error('❌ Sync completed with errors:', result.errors);
      
      return NextResponse.json({
        success: false,
        message: `Sync completed with errors. ${result.syncedCount}/${result.totalTemplates} templates synced successfully.`,
        details: {
          syncedCount: result.syncedCount,
          totalTemplates: result.totalTemplates,
          errors: result.errors
        }
      }, { status: 207 }); // 207 Multi-Status (partial success)
    }
    
  } catch (error) {
    console.error('❌ Immediate sync failed:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Failed to sync templates',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

/**
 * GET /api/admin/sync-now - Get sync status and trigger if needed
 */
export async function GET() {
  try {
    // Import storage manager to check current status
    const { validateStorageIntegrity } = await import('@/lib/storage-manager');
    
    const integrity = await validateStorageIntegrity();
    
    if (integrity.valid) {
      return NextResponse.json({
        success: true,
        message: 'All templates are already synced',
        needsSync: false,
        summary: integrity.summary
      });
    } else {
      return NextResponse.json({
        success: false,
        message: `Found ${integrity.issues.length} sync issues`,
        needsSync: true,
        issues: integrity.issues,
        summary: integrity.summary
      });
    }
    
  } catch (error) {
    console.error('Error checking sync status:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Failed to check sync status',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
