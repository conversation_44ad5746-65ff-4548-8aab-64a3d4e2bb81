import { join } from 'path';
import { existsSync } from 'fs';
import { readdir, stat } from 'fs/promises';
import {
  initializeTemplatesBucket,
  syncTemplateFilesToStorage,
  templateFilesExistInStorage
} from './supabase-storage';
import { getAllTemplates } from './database';

/**
 * Auto-sync system for ensuring templates are always available online
 */

let isAutoSyncRunning = false;
let lastSyncTime = 0;
const SYNC_INTERVAL = 5 * 60 * 1000; // 5 minutes
const FORCE_SYNC_INTERVAL = 30 * 60 * 1000; // 30 minutes

/**
 * Check if a template needs syncing
 */
async function templateNeedsSync(templateId: number, templateName: string): Promise<boolean> {
  try {
    // Check if files exist in storage
    const existsInStorage = await templateFilesExistInStorage(templateId);
    
    if (!existsInStorage) {
      return true; // Definitely needs sync
    }
    
    // Check if local files are newer than storage files
    const templatesDir = join(process.cwd(), 'public', 'templates');
    const templateDir = join(templatesDir, templateName);
    
    if (!existsSync(templateDir)) {
      return false; // No local files to sync
    }
    
    // Get local file modification times
    const files = await readdir(templateDir);
    let latestLocalTime = 0;
    
    for (const file of files) {
      const filePath = join(templateDir, file);
      const stats = await stat(filePath);
      if (stats.mtime.getTime() > latestLocalTime) {
        latestLocalTime = stats.mtime.getTime();
      }
    }
    
    // If local files are newer than last sync, sync is needed
    return latestLocalTime > lastSyncTime;
    
  } catch (error) {
    console.error(`Error checking sync status for template ${templateId}:`, error);
    return true; // Err on the side of syncing
  }
}

/**
 * Sync a single template if needed
 */
async function syncTemplateIfNeeded(templateId: number, templateName: string): Promise<boolean> {
  try {
    const needsSync = await templateNeedsSync(templateId, templateName);
    
    if (needsSync) {
      console.log(`Auto-syncing template ${templateId} (${templateName}) to storage...`);
      await syncTemplateFilesToStorage(templateId, templateName);
      console.log(`Template ${templateId} synced successfully`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error auto-syncing template ${templateId}:`, error);
    return false;
  }
}

/**
 * Run auto-sync for all templates
 */
export async function runAutoSync(force: boolean = false): Promise<{
  syncedCount: number;
  totalTemplates: number;
  errors: string[];
}> {
  if (isAutoSyncRunning && !force) {
    console.log('Auto-sync already running, skipping...');
    return { syncedCount: 0, totalTemplates: 0, errors: ['Auto-sync already running'] };
  }
  
  const now = Date.now();
  if (!force && (now - lastSyncTime) < SYNC_INTERVAL) {
    console.log('Auto-sync ran recently, skipping...');
    return { syncedCount: 0, totalTemplates: 0, errors: ['Sync ran recently'] };
  }
  
  isAutoSyncRunning = true;
  const errors: string[] = [];
  let syncedCount = 0;
  
  try {
    console.log('Starting auto-sync process...');
    
    // Ensure storage bucket exists
    await initializeTemplatesBucket();
    
    // Get all templates
    const templates = await getAllTemplates();
    console.log(`Found ${templates.length} templates to check`);
    
    // Sync each template if needed
    for (const template of templates) {
      try {
        const wasSynced = await syncTemplateIfNeeded(template.id, template.template_name);
        if (wasSynced) {
          syncedCount++;
        }
      } catch (error) {
        const errorMsg = `Failed to sync template ${template.id}: ${error}`;
        console.error(errorMsg);
        errors.push(errorMsg);
      }
    }
    
    lastSyncTime = now;
    console.log(`Auto-sync completed. Synced ${syncedCount}/${templates.length} templates`);
    
    return {
      syncedCount,
      totalTemplates: templates.length,
      errors
    };
    
  } catch (error) {
    const errorMsg = `Auto-sync failed: ${error}`;
    console.error(errorMsg);
    errors.push(errorMsg);
    
    return {
      syncedCount,
      totalTemplates: 0,
      errors
    };
  } finally {
    isAutoSyncRunning = false;
  }
}

/**
 * Start automatic background sync
 */
export function startAutoSync(): void {
  console.log('Starting automatic template sync service...');
  
  // Run initial sync
  runAutoSync(true).then(result => {
    console.log('Initial auto-sync completed:', result);
  }).catch(error => {
    console.error('Initial auto-sync failed:', error);
  });
  
  // Set up periodic sync
  setInterval(async () => {
    try {
      const result = await runAutoSync(false);
      if (result.syncedCount > 0) {
        console.log(`Periodic sync completed: ${result.syncedCount} templates synced`);
      }
    } catch (error) {
      console.error('Periodic sync error:', error);
    }
  }, SYNC_INTERVAL);
  
  // Set up force sync (every 30 minutes)
  setInterval(async () => {
    try {
      const result = await runAutoSync(true);
      console.log(`Force sync completed: ${result.syncedCount} templates synced`);
    } catch (error) {
      console.error('Force sync error:', error);
    }
  }, FORCE_SYNC_INTERVAL);
}

/**
 * Sync templates on demand (for API endpoints)
 */
export async function syncTemplatesOnDemand(): Promise<{
  success: boolean;
  syncedCount: number;
  totalTemplates: number;
  errors: string[];
}> {
  try {
    const result = await runAutoSync(true);
    return {
      success: result.errors.length === 0,
      ...result
    };
  } catch (error) {
    return {
      success: false,
      syncedCount: 0,
      totalTemplates: 0,
      errors: [error instanceof Error ? error.message : String(error)]
    };
  }
}

/**
 * Check if auto-sync is healthy
 */
export function getAutoSyncStatus(): {
  isRunning: boolean;
  lastSyncTime: number;
  timeSinceLastSync: number;
  nextSyncIn: number;
} {
  const now = Date.now();
  const timeSinceLastSync = now - lastSyncTime;
  const nextSyncIn = Math.max(0, SYNC_INTERVAL - timeSinceLastSync);
  
  return {
    isRunning: isAutoSyncRunning,
    lastSyncTime,
    timeSinceLastSync,
    nextSyncIn
  };
}
