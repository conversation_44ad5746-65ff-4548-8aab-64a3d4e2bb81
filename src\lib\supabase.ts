import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Client-side Supabase client (for browser usage)
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Server-side Supabase client with service role (for API routes)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Database types for TypeScript
export interface SupabaseSavedFile {
  id: number;
  document_data: string; // Base64 encoded
  document_metadata: string | null;
  user_id: number;
  created_at: string;
}

export interface SupabaseUser {
  id: number;
  username: string;
  password: string;
  recovery_key: string | null;
  role: string;
  created_at: string;
}

export interface SupabaseTemplate {
  id: number;
  template_name: string;
  description: string | null;
  filename: string;
  placeholders: string | null;
  layout_size: string | null;
  uploaded_at: string;
  user_id: number;
}

export interface SupabaseDocument {
  id: number;
  document_name: string;
  applicant_name: string;
  uploaded_at: string;
  document_data: string | null; // Base64 encoded
  status: string;
  approved_at: string | null;
  approved_by: number | null;
  user_id: number;
  code: string | null;
}

export interface SupabaseNotification {
  id: number;
  document_id: number;
  document_name: string;
  applicant_name: string;
  is_read: boolean;
  uploaded_at: string;
  user_id: number;
}

export interface SupabaseArchive {
  id: number;
  document_name: string;
  applicant_name: string;
  uploaded_at: string;
  document_data: string | null; // Base64 encoded
  status: string;
  user_id: number;
  document_id: number;
  approved_at: string | null;
  approved_by: number | null;
  code: string | null;
}

// Utility functions for data conversion
export function bufferToBase64(buffer: Buffer): string {
  return buffer.toString('base64');
}

export function base64ToBuffer(base64: string): Buffer {
  return Buffer.from(base64, 'base64');
}

// Error handling utility
export function handleSupabaseError(error: any, operation: string) {
  console.error(`Supabase ${operation} error:`, error);
  throw new Error(`Failed to ${operation}: ${error.message}`);
}
