import { supabaseAdmin, handleSupabaseError } from './supabase';
import type { Template } from './database';

// Template interface for compatibility
export interface Template<PERSON>ith<PERSON>ser extends Template {
  username: string;
}

/**
 * Create a new template in Supabase
 */
export async function createTemplateInSupabase(
  templateName: string,
  description: string | undefined,
  filename: string,
  placeholders: string | undefined,
  layoutSize: string | undefined,
  userId: number
): Promise<number> {
  try {
    const { data, error } = await supabaseAdmin
      .from('templates')
      .insert({
        template_name: templateName,
        description,
        filename,
        placeholders,
        layout_size: layoutSize,
        user_id: userId
      })
      .select('id')
      .single();

    if (error) {
      handleSupabaseError(error, 'create template in Supabase');
    }

    if (!data) {
      throw new Error('No data returned from Supabase');
    }

    return data.id;
  } catch (error) {
    handleSupabaseError(error, 'create template in Supabase');
    throw error;
  }
}

/**
 * Get a template by ID from Supabase
 */
export async function getTemplateByIdFromSupabase(id: number): Promise<Template | undefined> {
  try {
    const { data, error } = await supabaseAdmin
      .from('templates')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return undefined;
      }
      handleSupabaseError(error, 'get template by ID from Supabase');
    }

    if (!data) return undefined;

    return {
      id: data.id,
      template_name: data.template_name,
      description: data.description,
      filename: data.filename,
      placeholders: data.placeholders,
      layout_size: data.layout_size,
      uploaded_at: data.uploaded_at,
      user_id: data.user_id
    };
  } catch (error) {
    handleSupabaseError(error, 'get template by ID from Supabase');
    throw error;
  }
}

/**
 * Get all templates for a specific user from Supabase
 */
export async function getTemplatesByUserIdFromSupabase(userId: number): Promise<Template[]> {
  try {
    const { data, error } = await supabaseAdmin
      .from('templates')
      .select('*')
      .eq('user_id', userId)
      .order('uploaded_at', { ascending: false });

    if (error) {
      handleSupabaseError(error, 'get templates by user ID from Supabase');
    }

    if (!data) return [];

    return data.map(item => ({
      id: item.id,
      template_name: item.template_name,
      description: item.description,
      filename: item.filename,
      placeholders: item.placeholders,
      layout_size: item.layout_size,
      uploaded_at: item.uploaded_at,
      user_id: item.user_id
    }));
  } catch (error) {
    handleSupabaseError(error, 'get templates by user ID from Supabase');
    throw error;
  }
}

/**
 * Get all templates from Supabase
 */
export async function getAllTemplatesFromSupabase(): Promise<Template[]> {
  try {
    const { data, error } = await supabaseAdmin
      .from('templates')
      .select('*')
      .order('uploaded_at', { ascending: false });

    if (error) {
      handleSupabaseError(error, 'get all templates from Supabase');
    }

    if (!data) return [];

    return data.map(item => ({
      id: item.id,
      template_name: item.template_name,
      description: item.description,
      filename: item.filename,
      placeholders: item.placeholders,
      layout_size: item.layout_size,
      uploaded_at: item.uploaded_at,
      user_id: item.user_id
    }));
  } catch (error) {
    handleSupabaseError(error, 'get all templates from Supabase');
    throw error;
  }
}

/**
 * Update template information in Supabase
 */
export async function updateTemplateInSupabase(
  id: number,
  templateName: string,
  description: string | undefined,
  placeholders: string | undefined,
  layoutSize: string | undefined
): Promise<void> {
  try {
    const { error } = await supabaseAdmin
      .from('templates')
      .update({
        template_name: templateName,
        description,
        placeholders,
        layout_size: layoutSize
      })
      .eq('id', id);

    if (error) {
      handleSupabaseError(error, 'update template in Supabase');
    }
  } catch (error) {
    handleSupabaseError(error, 'update template in Supabase');
    throw error;
  }
}

/**
 * Delete a template from Supabase
 */
export async function deleteTemplateFromSupabase(id: number): Promise<void> {
  try {
    const { error } = await supabaseAdmin
      .from('templates')
      .delete()
      .eq('id', id);

    if (error) {
      handleSupabaseError(error, 'delete template from Supabase');
    }
  } catch (error) {
    handleSupabaseError(error, 'delete template from Supabase');
    throw error;
  }
}

/**
 * Get templates with user information from Supabase (JOIN query)
 */
export async function getTemplatesWithUserInfoFromSupabase(): Promise<(Template & { username: string })[]> {
  try {
    const { data, error } = await supabaseAdmin
      .from('templates')
      .select(`
        *,
        users!inner(username)
      `)
      .order('uploaded_at', { ascending: false });

    if (error) {
      handleSupabaseError(error, 'get templates with user info from Supabase');
    }

    if (!data) return [];

    return data.map(item => ({
      id: item.id,
      template_name: item.template_name,
      description: item.description,
      filename: item.filename,
      placeholders: item.placeholders,
      layout_size: item.layout_size,
      uploaded_at: item.uploaded_at,
      user_id: item.user_id,
      username: item.users.username
    }));
  } catch (error) {
    handleSupabaseError(error, 'get templates with user info from Supabase');
    throw error;
  }
}

/**
 * Sync template from local SQLite to Supabase
 */
export async function syncTemplateToSupabase(template: Template): Promise<void> {
  try {
    // Check if template already exists in Supabase
    const existingTemplate = await getTemplateByIdFromSupabase(template.id);
    
    if (existingTemplate) {
      // Update existing template
      const { error } = await supabaseAdmin
        .from('templates')
        .update({
          template_name: template.template_name,
          description: template.description,
          filename: template.filename,
          placeholders: template.placeholders,
          layout_size: template.layout_size,
          uploaded_at: template.uploaded_at,
          user_id: template.user_id
        })
        .eq('id', template.id);

      if (error) {
        handleSupabaseError(error, 'sync template to Supabase (update)');
      }
    } else {
      // Insert new template with original ID
      const { error } = await supabaseAdmin
        .from('templates')
        .insert({
          id: template.id,
          template_name: template.template_name,
          description: template.description,
          filename: template.filename,
          placeholders: template.placeholders,
          layout_size: template.layout_size,
          uploaded_at: template.uploaded_at,
          user_id: template.user_id
        });

      if (error) {
        handleSupabaseError(error, 'sync template to Supabase (insert)');
      }
    }
  } catch (error) {
    handleSupabaseError(error, 'sync template to Supabase');
    throw error;
  }
}

/**
 * Sync template from Supabase to local SQLite
 */
export async function syncTemplateToLocal(template: Template): Promise<void> {
  try {
    // Import local database functions
    const { getTemplateById, runQuery } = await import('./database');
    
    // Check if template already exists locally
    const existingTemplate = await getTemplateById(template.id);
    
    if (existingTemplate) {
      // Update existing template
      await runQuery(
        'UPDATE templates SET template_name = ?, description = ?, filename = ?, placeholders = ?, layout_size = ?, uploaded_at = ?, user_id = ? WHERE id = ?',
        [template.template_name, template.description, template.filename, template.placeholders, template.layout_size, template.uploaded_at, template.user_id, template.id]
      );
    } else {
      // Insert new template with original ID
      await runQuery(
        'INSERT INTO templates (id, template_name, description, filename, placeholders, layout_size, uploaded_at, user_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [template.id, template.template_name, template.description, template.filename, template.placeholders, template.layout_size, template.uploaded_at, template.user_id]
      );
    }
  } catch (error) {
    console.error('Error syncing template to local database:', error);
    throw error;
  }
}
