import { NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';

/**
 * GET /api/test/supabase-storage - Test Supabase Storage connection
 */
export async function GET() {
  try {
    console.log('Testing Supabase Storage connection...');
    
    // Test 1: List buckets
    console.log('Test 1: Listing storage buckets...');
    const { data: buckets, error: listError } = await supabaseAdmin.storage.listBuckets();
    
    if (listError) {
      console.error('Error listing buckets:', listError);
      return NextResponse.json({
        success: false,
        error: 'Failed to list buckets',
        details: listError
      }, { status: 500 });
    }
    
    console.log('Buckets found:', buckets);
    
    // Test 2: Try to create a test bucket
    console.log('Test 2: Creating test bucket...');
    const testBucketName = 'test-bucket-' + Date.now();
    const { data: createData, error: createError } = await supabaseAdmin.storage.createBucket(testBucketName, {
      public: false,
      allowedMimeTypes: ['text/plain'],
      fileSizeLimit: 1024 * 1024 // 1MB
    });
    
    if (createError) {
      console.error('Error creating test bucket:', createError);
      return NextResponse.json({
        success: false,
        error: 'Failed to create test bucket',
        details: createError,
        buckets: buckets
      }, { status: 500 });
    }
    
    console.log('Test bucket created:', createData);
    
    // Test 3: Upload a test file
    console.log('Test 3: Uploading test file...');
    const testContent = 'Hello, Supabase Storage!';
    const testBuffer = Buffer.from(testContent, 'utf-8');
    
    const { data: uploadData, error: uploadError } = await supabaseAdmin.storage
      .from(testBucketName)
      .upload('test-file.txt', testBuffer, {
        contentType: 'text/plain'
      });
    
    if (uploadError) {
      console.error('Error uploading test file:', uploadError);
      
      // Clean up: delete test bucket
      await supabaseAdmin.storage.deleteBucket(testBucketName);
      
      return NextResponse.json({
        success: false,
        error: 'Failed to upload test file',
        details: uploadError,
        buckets: buckets
      }, { status: 500 });
    }
    
    console.log('Test file uploaded:', uploadData);
    
    // Test 4: Download the test file
    console.log('Test 4: Downloading test file...');
    const { data: downloadData, error: downloadError } = await supabaseAdmin.storage
      .from(testBucketName)
      .download('test-file.txt');
    
    if (downloadError) {
      console.error('Error downloading test file:', downloadError);
      
      // Clean up: delete test bucket
      await supabaseAdmin.storage.deleteBucket(testBucketName);
      
      return NextResponse.json({
        success: false,
        error: 'Failed to download test file',
        details: downloadError,
        buckets: buckets
      }, { status: 500 });
    }
    
    const downloadedContent = await downloadData.text();
    console.log('Downloaded content:', downloadedContent);
    
    // Test 5: Clean up - delete test file and bucket
    console.log('Test 5: Cleaning up...');
    const { error: deleteFileError } = await supabaseAdmin.storage
      .from(testBucketName)
      .remove(['test-file.txt']);
    
    if (deleteFileError) {
      console.error('Error deleting test file:', deleteFileError);
    }
    
    const { error: deleteBucketError } = await supabaseAdmin.storage.deleteBucket(testBucketName);
    
    if (deleteBucketError) {
      console.error('Error deleting test bucket:', deleteBucketError);
    }
    
    // Return success
    return NextResponse.json({
      success: true,
      message: 'Supabase Storage test completed successfully',
      tests: {
        listBuckets: { success: true, count: buckets?.length || 0 },
        createBucket: { success: true, name: testBucketName },
        uploadFile: { success: true, path: uploadData?.path },
        downloadFile: { success: true, content: downloadedContent },
        cleanup: { success: !deleteFileError && !deleteBucketError }
      },
      buckets: buckets
    });
    
  } catch (error) {
    console.error('Unexpected error during Supabase Storage test:', error);
    return NextResponse.json({
      success: false,
      error: 'Unexpected error during test',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
