import { NextResponse } from 'next/server';
import { initializeTemplatesBucket } from '@/lib/supabase-storage';

/**
 * POST /api/admin/init-storage - Initialize Supabase Storage
 */
export async function POST() {
  try {
    console.log('Initializing Supabase Storage...');
    
    // Initialize templates bucket
    await initializeTemplatesBucket();
    
    return NextResponse.json(
      { 
        message: 'Supabase Storage initialized successfully',
        bucket: 'templates'
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error initializing Supabase Storage:', error);
    return NextResponse.json(
      { error: 'Failed to initialize Supabase Storage' },
      { status: 500 }
    );
  }
}
