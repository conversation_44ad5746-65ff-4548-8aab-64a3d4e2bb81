import { NextRequest, NextResponse } from 'next/server';
import { getAllUsers, createUser, getUserByUsername } from '@/lib/user-sync';
import bcrypt from 'bcryptjs';

/**
 * GET /api/users - Get all users
 */
export async function GET() {
  try {
    const users = await getAllUsers();
    
    // Remove password and recovery_key from response for security
    const safeUsers = users.map(user => ({
      id: user.id,
      username: user.username,
      created_at: user.created_at
    }));
    
    return NextResponse.json({ users: safeUsers });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/users - Create a new user
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password, recoveryKey } = body;
    
    // Validate required fields
    if (!username || !password) {
      return NextResponse.json(
        { error: 'Username and password are required' },
        { status: 400 }
      );
    }
    
    // Check if user already exists
    const existingUser = await getUserByUsername(username);
    if (existingUser) {
      return NextResponse.json(
        { error: 'Username already exists' },
        { status: 409 }
      );
    }
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12);
    
    // Create the user
    const userId = await createUser(username, hashedPassword, recoveryKey);
    
    return NextResponse.json(
      { 
        message: 'User created successfully',
        userId,
        username
      },
      { status: 201 }
    );
    
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}
