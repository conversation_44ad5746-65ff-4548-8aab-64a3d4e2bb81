import { startAutoSync } from './auto-sync';

/**
 * Application startup initialization
 */

let isInitialized = false;

export function initializeApplication(): void {
  if (isInitialized) {
    return;
  }
  
  console.log('🚀 Initializing LDIS application...');
  
  try {
    // Start auto-sync service
    console.log('📁 Starting template auto-sync service...');
    startAutoSync();
    
    isInitialized = true;
    console.log('✅ LDIS application initialized successfully');
    
  } catch (error) {
    console.error('❌ Error initializing LDIS application:', error);
  }
}

// Auto-initialize when this module is imported (server-side only)
if (typeof window === 'undefined') {
  // Delay initialization slightly to ensure all modules are loaded
  setTimeout(() => {
    initializeApplication();
  }, 1000);
}
