import type { User } from './database';
import {
  createUserInSupabase,
  getUserByUsernameFromSupabase,
  getUserByIdFromSupabase,
  getAllUsersFromSupabase,
  updateUserPasswordInSupabase,
  updateUserRecoveryKeyInSupabase,
  deleteUser<PERSON>romSupabase,
  syncUserToSupabase,
  syncUserToLocal
} from './supabase-users';

/**
 * Create a new user with bidirectional sync
 */
export async function createUser(
  username: string,
  hashedPassword: string,
  recoveryKey?: string,
  role: 'admin' | 'regular' = 'admin'
): Promise<number> {
  try {
    // Import local database functions
    const { createUser: createUserLocal } = await import('./database');
    
    // Create user locally first
    const localUserId = await createUserLocal(username, hashedPassword, recoveryKey, role);
    
    // Get the created user to sync to Supabase
    const { getUserById } = await import('./database');
    const createdUser = await getUserById(localUserId);
    
    if (createdUser) {
      // Sync to Supabase
      await syncUserToSupabase(createdUser);
    }
    
    return localUserId;
  } catch (error) {
    console.error('Error creating user with sync:', error);
    throw error;
  }
}

/**
 * Get a user by username with fallback to both sources
 */
export async function getUserByUsername(username: string): Promise<User | undefined> {
  try {
    // Try local first
    const { getUserByUsername: getUserByUsernameLocal } = await import('./database');
    let user = await getUserByUsernameLocal(username);
    
    if (!user) {
      // Try Supabase if not found locally
      user = await getUserByUsernameFromSupabase(username);
      
      if (user) {
        // Sync to local if found in Supabase
        await syncUserToLocal(user);
      }
    }
    
    return user;
  } catch (error) {
    console.error('Error getting user by username:', error);
    throw error;
  }
}

/**
 * Get a user by ID with fallback to both sources
 */
export async function getUserById(id: number): Promise<User | undefined> {
  try {
    // Try local first
    const { getUserById: getUserByIdLocal } = await import('./database');
    let user = await getUserByIdLocal(id);
    
    if (!user) {
      // Try Supabase if not found locally
      user = await getUserByIdFromSupabase(id);
      
      if (user) {
        // Sync to local if found in Supabase
        await syncUserToLocal(user);
      }
    }
    
    return user;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    throw error;
  }
}

/**
 * Get all users with sync from both sources
 */
export async function getAllUsers(): Promise<User[]> {
  try {
    // Get users from both sources
    const { getAllUsers: getAllUsersLocal } = await import('./database');
    const localUsers = await getAllUsersLocal();
    const supabaseUsers = await getAllUsersFromSupabase();
    
    // Create a map to merge users by ID
    const userMap = new Map<number, User>();
    
    // Add local users first
    localUsers.forEach(user => {
      userMap.set(user.id, user);
    });
    
    // Add/update with Supabase users
    supabaseUsers.forEach(user => {
      const existingUser = userMap.get(user.id);
      if (!existingUser || new Date(user.created_at) > new Date(existingUser.created_at)) {
        userMap.set(user.id, user);
        // Sync to local if newer or missing
        syncUserToLocal(user).catch(console.error);
      }
    });
    
    // Sync local users to Supabase if they don't exist there
    localUsers.forEach(user => {
      const supabaseUser = supabaseUsers.find(su => su.id === user.id);
      if (!supabaseUser) {
        syncUserToSupabase(user).catch(console.error);
      }
    });
    
    return Array.from(userMap.values()).sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  } catch (error) {
    console.error('Error getting all users:', error);
    throw error;
  }
}

/**
 * Update user password with bidirectional sync
 */
export async function updateUserPassword(id: number, hashedPassword: string): Promise<void> {
  try {
    // Update locally
    const { updateUserPassword: updateUserPasswordLocal } = await import('./database');
    await updateUserPasswordLocal(id, hashedPassword);
    
    // Update in Supabase
    await updateUserPasswordInSupabase(id, hashedPassword);
  } catch (error) {
    console.error('Error updating user password:', error);
    throw error;
  }
}

/**
 * Update user recovery key with bidirectional sync
 */
export async function updateUserRecoveryKey(id: number, recoveryKey: string): Promise<void> {
  try {
    // Update locally
    const { updateUserRecoveryKey: updateUserRecoveryKeyLocal } = await import('./database');
    await updateUserRecoveryKeyLocal(id, recoveryKey);
    
    // Update in Supabase
    await updateUserRecoveryKeyInSupabase(id, recoveryKey);
  } catch (error) {
    console.error('Error updating user recovery key:', error);
    throw error;
  }
}

/**
 * Delete a user with bidirectional sync
 */
export async function deleteUser(id: number): Promise<void> {
  try {
    // Delete locally
    const { deleteUser: deleteUserLocal } = await import('./database');
    await deleteUserLocal(id);
    
    // Delete from Supabase
    await deleteUserFromSupabase(id);
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
}

/**
 * Sync all users between local and Supabase
 */
export async function syncAllUsers(): Promise<void> {
  try {
    console.log('Starting user synchronization...');
    
    // Get all users to trigger sync
    await getAllUsers();
    
    console.log('User synchronization completed.');
  } catch (error) {
    console.error('Error during user synchronization:', error);
    throw error;
  }
}
