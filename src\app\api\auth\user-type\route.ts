import { NextRequest, NextResponse } from 'next/server';
import { getUserByUsername } from '@/lib/database';

/**
 * GET /api/auth/user-type - Get user type by username
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const username = searchParams.get('username');
    
    if (!username) {
      return NextResponse.json(
        { error: 'Username is required' },
        { status: 400 }
      );
    }
    
    // Get user from database
    const user = await getUserByUsername(username);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      {
        userType: user.role
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error getting user type:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
