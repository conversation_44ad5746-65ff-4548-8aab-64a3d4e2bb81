import { NextRequest, NextResponse } from 'next/server';
import { createSavedFile, getSavedFilesByUserId } from '@/lib/database';

/**
 * GET /api/saved-files - Get all saved files for a user
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const savedFiles = await getSavedFilesByUserId(parseInt(userId));
    
    return NextResponse.json({ savedFiles });
  } catch (error) {
    console.error('Error fetching saved files:', error);
    return NextResponse.json(
      { error: 'Failed to fetch saved files' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/saved-files - Create a new saved file
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      documentData,
      metadata,
      userId
    } = body;

    if (!documentData || !metadata || !userId) {
      return NextResponse.json(
        { error: 'Document data, metadata, and user ID are required' },
        { status: 400 }
      );
    }

    // Convert base64 document data to buffer
    const buffer = Buffer.from(documentData, 'base64');

    // Create saved file with metadata
    const savedFileId = await createSavedFile(
      buffer,
      metadata,
      parseInt(userId)
    );

    return NextResponse.json(
      {
        message: 'File saved successfully',
        savedFileId
      },
      { status: 201 }
    );
    
  } catch (error) {
    console.error('Error saving file:', error);
    return NextResponse.json(
      { error: 'Failed to save file' },
      { status: 500 }
    );
  }
}
