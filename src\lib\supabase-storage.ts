import { supabaseAdmin, handleSupabaseError } from './supabase';
import { readFile, writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

// Storage bucket name for templates
export const TEMPLATES_BUCKET = 'templates';

/**
 * Initialize storage bucket for templates
 */
export async function initializeTemplatesBucket(): Promise<void> {
  try {
    console.log('Initializing templates bucket...');

    // Check if bucket exists
    console.log('Checking if bucket exists...');
    const { data: buckets, error: listError } = await supabaseAdmin.storage.listBuckets();

    if (listError) {
      console.error('Error listing buckets:', listError);
      handleSupabaseError(listError, 'list storage buckets');
      return;
    }

    console.log('Found buckets:', buckets?.map(b => b.name) || []);
    const templatesBucket = buckets?.find(bucket => bucket.name === TEMPLATES_BUCKET);

    if (!templatesBucket) {
      console.log(`Creating bucket: ${TEMPLATES_BUCKET}`);

      // Create the bucket with minimal configuration first
      const { error: createError } = await supabaseAdmin.storage.createBucket(TEMPLATES_BUCKET, {
        public: false
      });

      if (createError) {
        console.error('Error creating bucket:', createError);

        // Try with different configuration if first attempt fails
        if (createError.message?.includes('already exists')) {
          console.log('Bucket already exists (race condition)');
          return;
        }

        // Try creating with public access if private fails
        console.log('Retrying with public bucket...');
        const { error: publicCreateError } = await supabaseAdmin.storage.createBucket(TEMPLATES_BUCKET, {
          public: true
        });

        if (publicCreateError) {
          console.error('Error creating public bucket:', publicCreateError);
          handleSupabaseError(publicCreateError, 'create templates bucket');
          return;
        }

        console.log('Templates storage bucket created successfully (public)');
      } else {
        console.log('Templates storage bucket created successfully (private)');
      }
    } else {
      console.log('Templates storage bucket already exists');
    }
  } catch (error) {
    console.error('Error initializing templates bucket:', error);
    throw error;
  }
}

/**
 * Upload a file to Supabase Storage
 */
export async function uploadFileToStorage(
  filePath: string,
  fileBuffer: Buffer,
  contentType?: string
): Promise<string> {
  try {
    console.log(`Uploading file to storage: ${filePath}, size: ${fileBuffer.length} bytes`);

    // Ensure bucket exists first
    await initializeTemplatesBucket();

    const { data, error } = await supabaseAdmin.storage
      .from(TEMPLATES_BUCKET)
      .upload(filePath, fileBuffer, {
        contentType: contentType || 'application/octet-stream',
        upsert: true
      });

    if (error) {
      console.error('Storage upload error:', error);
      handleSupabaseError(error, 'upload file to storage');
    }

    if (!data) {
      throw new Error('No data returned from storage upload');
    }

    console.log(`File uploaded successfully: ${data.path}`);
    return data.path;
  } catch (error) {
    console.error('Upload file to storage error:', error);
    handleSupabaseError(error, 'upload file to storage');
    throw error;
  }
}

/**
 * Download a file from Supabase Storage
 */
export async function downloadFileFromStorage(filePath: string): Promise<Buffer> {
  try {
    const { data, error } = await supabaseAdmin.storage
      .from(TEMPLATES_BUCKET)
      .download(filePath);

    if (error) {
      handleSupabaseError(error, 'download file from storage');
    }

    if (!data) {
      throw new Error('No data returned from storage download');
    }

    // Convert Blob to Buffer
    const arrayBuffer = await data.arrayBuffer();
    return Buffer.from(arrayBuffer);
  } catch (error) {
    handleSupabaseError(error, 'download file from storage');
    throw error;
  }
}

/**
 * Delete a file from Supabase Storage
 */
export async function deleteFileFromStorage(filePath: string): Promise<void> {
  try {
    const { error } = await supabaseAdmin.storage
      .from(TEMPLATES_BUCKET)
      .remove([filePath]);

    if (error) {
      handleSupabaseError(error, 'delete file from storage');
    }
  } catch (error) {
    handleSupabaseError(error, 'delete file from storage');
    throw error;
  }
}

/**
 * List files in a storage directory
 */
export async function listFilesInStorage(directoryPath: string = ''): Promise<any[]> {
  try {
    const { data, error } = await supabaseAdmin.storage
      .from(TEMPLATES_BUCKET)
      .list(directoryPath);

    if (error) {
      handleSupabaseError(error, 'list files in storage');
    }

    return data || [];
  } catch (error) {
    handleSupabaseError(error, 'list files in storage');
    throw error;
  }
}

/**
 * Get public URL for a file (if bucket is public)
 */
export function getPublicUrl(filePath: string): string {
  const { data } = supabaseAdmin.storage
    .from(TEMPLATES_BUCKET)
    .getPublicUrl(filePath);

  return data.publicUrl;
}

/**
 * Create a signed URL for private file access
 */
export async function createSignedUrl(filePath: string, expiresIn: number = 3600): Promise<string> {
  try {
    const { data, error } = await supabaseAdmin.storage
      .from(TEMPLATES_BUCKET)
      .createSignedUrl(filePath, expiresIn);

    if (error) {
      handleSupabaseError(error, 'create signed URL');
    }

    if (!data) {
      throw new Error('No data returned from signed URL creation');
    }

    return data.signedUrl;
  } catch (error) {
    handleSupabaseError(error, 'create signed URL');
    throw error;
  }
}

/**
 * Sync local template files to Supabase Storage
 */
export async function syncTemplateFilesToStorage(templateId: number, templateName: string): Promise<void> {
  try {
    const templatesDir = join(process.cwd(), 'templates');
    const templateDir = join(templatesDir, templateId.toString());
    
    if (!existsSync(templateDir)) {
      console.log(`Template directory ${templateDir} does not exist, skipping sync`);
      return;
    }

    // Read directory contents
    const fs = await import('fs/promises');
    const files = await fs.readdir(templateDir);
    
    for (const file of files) {
      const localFilePath = join(templateDir, file);
      const storageFilePath = `${templateId}/${file}`;
      
      try {
        // Read local file
        const fileBuffer = await readFile(localFilePath);
        
        // Determine content type
        let contentType = 'application/octet-stream';
        if (file.endsWith('.html')) {
          contentType = 'text/html';
        } else if (file.endsWith('.png')) {
          contentType = 'image/png';
        } else if (file.endsWith('.jpg') || file.endsWith('.jpeg')) {
          contentType = 'image/jpeg';
        } else if (file.endsWith('.gif')) {
          contentType = 'image/gif';
        } else if (file.endsWith('.svg')) {
          contentType = 'image/svg+xml';
        }
        
        // Upload to storage
        await uploadFileToStorage(storageFilePath, fileBuffer, contentType);
        console.log(`Synced ${file} to storage for template ${templateId}`);
        
      } catch (fileError) {
        console.error(`Error syncing file ${file} for template ${templateId}:`, fileError);
      }
    }
  } catch (error) {
    console.error(`Error syncing template files for template ${templateId}:`, error);
    throw error;
  }
}

/**
 * Sync template files from Supabase Storage to local
 */
export async function syncTemplateFilesFromStorage(templateId: number): Promise<void> {
  try {
    const templatesDir = join(process.cwd(), 'templates');
    const templateDir = join(templatesDir, templateId.toString());
    
    // Ensure template directory exists
    if (!existsSync(templateDir)) {
      await mkdir(templateDir, { recursive: true });
    }
    
    // List files in storage for this template
    const storageFiles = await listFilesInStorage(templateId.toString());
    
    for (const file of storageFiles) {
      if (file.name && !file.name.endsWith('/')) { // Skip directories
        const storageFilePath = `${templateId}/${file.name}`;
        const localFilePath = join(templateDir, file.name);
        
        try {
          // Download from storage
          const fileBuffer = await downloadFileFromStorage(storageFilePath);
          
          // Write to local file
          await writeFile(localFilePath, fileBuffer);
          console.log(`Synced ${file.name} from storage to local for template ${templateId}`);
          
        } catch (fileError) {
          console.error(`Error syncing file ${file.name} from storage for template ${templateId}:`, fileError);
        }
      }
    }
  } catch (error) {
    console.error(`Error syncing template files from storage for template ${templateId}:`, error);
    throw error;
  }
}

/**
 * Check if template files exist in storage
 */
export async function templateFilesExistInStorage(templateId: number): Promise<boolean> {
  try {
    const files = await listFilesInStorage(templateId.toString());
    return files.length > 0;
  } catch (error) {
    console.error(`Error checking template files in storage for template ${templateId}:`, error);
    return false;
  }
}
