import { NextResponse } from 'next/server';
import { syncAllTemplateFiles } from '@/lib/template-sync';

/**
 * POST /api/admin/sync-files - Sync all template files between local and Supabase Storage
 */
export async function POST() {
  try {
    console.log('Starting template files synchronization...');
    
    // Sync all template files
    await syncAllTemplateFiles();
    
    return NextResponse.json(
      { 
        message: 'Template files synchronized successfully'
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error('Error synchronizing template files:', error);
    return NextResponse.json(
      { error: 'Failed to synchronize template files' },
      { status: 500 }
    );
  }
}
