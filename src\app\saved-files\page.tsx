"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { useUserAuth } from "@/hooks/use-local-storage";
import {
  Download,
  Trash2,
  FileText,
  AlertCircle,
  RefreshCw,
} from "lucide-react";
import { toast } from "sonner";

interface SavedFile {
  id: number;
  document_data: Buffer;
  document_metadata?: string;
  user_id: number;
  created_at: string;
}

interface SavedFileMetadata {
  document_name: string;
  applicant_name: string;
  template_id?: number;
  template_name: string;
  file_status: string;
  generation_timestamp: string;
  form_data?: any;
  document_info?: any;
}

export default function SavedFilesPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useUserAuth();
  const [savedFiles, setSavedFiles] = useState<SavedFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Fetch saved files
  const fetchSavedFiles = async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const response = await fetch(`/api/saved-files?userId=${user.id}`);
      const data = await response.json();

      if (response.ok) {
        setSavedFiles(data.savedFiles);
      } else {
        setError(data.error || "Failed to fetch saved files");
      }
    } catch (err) {
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Download a saved file
  const downloadFile = async (fileId: number) => {
    try {
      const response = await fetch(`/api/saved-files/${fileId}`);

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;

        // Get filename from Content-Disposition header or use default
        const contentDisposition = response.headers.get("Content-Disposition");
        let filename = `saved-file-${fileId}.pdf`;
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/);
          if (filenameMatch) {
            filename = filenameMatch[1];
          }
        }

        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success("File downloaded successfully");
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to download file");
      }
    } catch (err) {
      toast.error("Network error. Please try again.");
    }
  };

  // Delete a saved file
  const deleteFile = async (fileId: number) => {
    if (!confirm("Are you sure you want to delete this file?")) return;

    try {
      const response = await fetch(`/api/saved-files/${fileId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        setSavedFiles((files) => files.filter((file) => file.id !== fileId));
        toast.success("File deleted successfully");
      } else {
        const data = await response.json();
        toast.error(data.error || "Failed to delete file");
      }
    } catch (err) {
      toast.error("Network error. Please try again.");
    }
  };

  useEffect(() => {
    if (!authLoading && isAuthenticated && user) {
      fetchSavedFiles();
    }
  }, [authLoading, isAuthenticated, user]);

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <div className="space-y-6">
        <div>
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-96 mt-2" />
        </div>
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Show error if user is not authenticated or not a regular user
  if (!isAuthenticated || user?.role !== "regular") {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Saved Files</h1>
          <p className="text-muted-foreground">
            Access denied. This page is only available to regular users.
          </p>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You need to be logged in as a regular user to access saved files.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Saved Files
              </CardTitle>
              <CardDescription>
                View and manage your automatically saved generated documents.
                Showing {savedFiles.length} saved file
                {savedFiles.length !== 1 ? "s" : ""}.
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchSavedFiles}
              disabled={loading}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {loading ? (
            <div className="grid gap-4">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-32 w-full" />
              ))}
            </div>
          ) : savedFiles.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No saved files</h3>
              <p className="text-muted-foreground text-center max-w-md mx-auto">
                You haven't generated any documents yet. When you generate
                documents using templates, they will be automatically saved here
                for easy access and reuse.
              </p>
            </div>
          ) : (
            <div className="grid gap-4">
              {savedFiles.map((file) => {
                // Parse metadata
                let metadata: SavedFileMetadata | null = null;
                if (file.document_metadata) {
                  try {
                    metadata = JSON.parse(file.document_metadata);
                  } catch (error) {
                    console.error("Error parsing metadata:", error);
                  }
                }

                return (
                  <Card key={file.id} className="border-l-4 border-l-blue-500">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="flex items-center gap-2 text-lg">
                            <FileText className="h-5 w-5 text-blue-600" />
                            {metadata?.document_name ||
                              `Saved File #${file.id}`}
                          </CardTitle>
                          <div className="mt-2 space-y-1 text-sm text-muted-foreground">
                            {metadata?.applicant_name && (
                              <div className="flex items-center gap-2">
                                <span className="font-medium">Applicant:</span>
                                <span>{metadata.applicant_name}</span>
                              </div>
                            )}
                            {metadata?.template_name && (
                              <div className="flex items-center gap-2">
                                <span className="font-medium">Template:</span>
                                <span>{metadata.template_name}</span>
                              </div>
                            )}
                            {metadata?.file_status && (
                              <div className="flex items-center gap-2">
                                <span className="font-medium">Status:</span>
                                <Badge variant="secondary" className="text-xs">
                                  {metadata.file_status}
                                </Badge>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex gap-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => downloadFile(file.id)}
                            className="flex items-center gap-2"
                          >
                            <Download className="h-4 w-4" />
                            Download
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deleteFile(file.id)}
                            className="flex items-center gap-2 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <div className="flex items-center gap-4">
                          {metadata?.generation_timestamp && (
                            <span>
                              Generated:{" "}
                              {new Date(
                                metadata.generation_timestamp
                              ).toLocaleDateString("en-US", {
                                year: "numeric",
                                month: "short",
                                day: "numeric",
                                hour: "2-digit",
                                minute: "2-digit",
                              })}
                            </span>
                          )}
                          <span>
                            Saved:{" "}
                            {new Date(file.created_at).toLocaleDateString(
                              "en-US",
                              {
                                year: "numeric",
                                month: "short",
                                day: "numeric",
                                hour: "2-digit",
                                minute: "2-digit",
                              }
                            )}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
